import React, { useState, useEffect, useRef } from 'react';
import {
  Input,
  AutoComplete,
  Card,
  Typography,
  Space,
  Tag,
  Avatar,
  Spin,
  Empty,
  Button,
} from 'antd';
import {
  SearchOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  UserOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import moment from 'moment';
import { useSearchEvents } from '@/hooks/useEvents';
import { Event, EventType, Department } from '@gbf-calendar/shared';
import { debounce } from 'lodash';

const { Text } = Typography;

interface SearchResult {
  key: string;
  value: string;
  event: Event;
}

interface GlobalSearchProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  onEventSelect?: (event: Event) => void;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({
  placeholder = "Search events, locations, organizers...",
  size = 'middle',
  style,
  onEventSelect,
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [options, setOptions] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const searchRef = useRef<any>(null);

  const { data: searchResults, isLoading } = useSearchEvents(
    searchValue,
    10,
    searchValue.length >= 2
  );

  // Debounced search to avoid too many API calls
  const debouncedSearch = debounce((value: string) => {
    if (value.length >= 2) {
      // The search will be triggered by the useSearchEvents hook
    } else {
      setOptions([]);
    }
  }, 300);

  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue]);

  useEffect(() => {
    if (searchResults) {
      const formattedOptions: SearchResult[] = searchResults.map((event) => ({
        key: event._id || '',
        value: event.title,
        event,
      }));
      setOptions(formattedOptions);
    }
  }, [searchResults]);

  const getDepartmentColor = (department: Department) => {
    const colors = {
      [Department.EXECUTIVE]: 'purple',
      [Department.FINANCE]: 'green',
      [Department.OPERATIONS]: 'blue',
      [Department.HUMAN_RESOURCES]: 'orange',
      [Department.MARKETING]: 'pink',
      [Department.SALES]: 'cyan',
      [Department.TECHNOLOGY]: 'geekblue',
      [Department.LEGAL]: 'red',
      [Department.COMPLIANCE]: 'yellow',
      [Department.STRATEGY]: 'magenta',
      [Department.RESEARCH]: 'lime',
      [Department.CUSTOMER_SERVICE]: 'volcano',
      [Department.ADMINISTRATION]: 'gold',
      [Department.OTHER]: 'default',
    };
    return colors[department] || 'default';
  };

  const formatEventType = (type: EventType) => {
    return type.replace(/_/g, ' ').toUpperCase();
  };

  const formatDepartment = (department: Department) => {
    return department.replace(/_/g, ' ').toUpperCase();
  };

  const isEventUpcoming = (startDate: Date) => {
    return moment(startDate).isAfter(moment());
  };

  const isEventOngoing = (startDate: Date, endDate: Date) => {
    const now = moment();
    return now.isBetween(moment(startDate), moment(endDate));
  };

  const renderOption = (option: SearchResult) => {
    const { event } = option;
    const isUpcoming = isEventUpcoming(event.startDate);
    const isOngoing = isEventOngoing(event.startDate, event.endDate);

    return (
      <div
        style={{
          padding: '12px 16px',
          borderBottom: '1px solid #f0f0f0',
          cursor: 'pointer',
        }}
        onClick={() => handleEventSelect(event)}
      >
        <Space direction="vertical" size={4} style={{ width: '100%' }}>
          {/* Event Title and Status */}
          <Space>
            <Avatar
              size="small"
              style={{
                backgroundColor: getDepartmentColor(event.department),
                color: '#fff',
              }}
            >
              {event.department.charAt(0).toUpperCase()}
            </Avatar>
            <Text strong style={{ fontSize: '14px' }}>
              {event.title}
            </Text>
            {isOngoing && <Tag color="green" size="small">LIVE</Tag>}
            {isUpcoming && <Tag color="blue" size="small">UPCOMING</Tag>}
          </Space>

          {/* Event Details */}
          <Space wrap size={[8, 4]}>
            <Space size={4}>
              <CalendarOutlined style={{ color: '#666', fontSize: '12px' }} />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {moment(event.startDate).format('MMM DD, YYYY')}
              </Text>
            </Space>
            <Space size={4}>
              <ClockCircleOutlined style={{ color: '#666', fontSize: '12px' }} />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {moment(event.startDate).format('HH:mm')}
              </Text>
            </Space>
            {event.location && (
              <Space size={4}>
                <EnvironmentOutlined style={{ color: '#666', fontSize: '12px' }} />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {event.location}
                </Text>
              </Space>
            )}
            {event.spoc && (
              <Space size={4}>
                <UserOutlined style={{ color: '#666', fontSize: '12px' }} />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {event.spoc}
                </Text>
              </Space>
            )}
          </Space>

          {/* Tags */}
          <Space wrap size={[4, 2]}>
            <Tag size="small" color={getDepartmentColor(event.department)}>
              {formatDepartment(event.department)}
            </Tag>
            <Tag size="small">{formatEventType(event.type)}</Tag>
            <Tag size="small" color={event.priority === 'high' || event.priority === 'urgent' ? 'red' : 'default'}>
              {event.priority.toUpperCase()}
            </Tag>
          </Space>
        </Space>
      </div>
    );
  };

  const handleEventSelect = (event: Event) => {
    setSearchValue('');
    setIsOpen(false);
    if (onEventSelect) {
      onEventSelect(event);
    } else {
      navigate(`/events/${event._id}`);
    }
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    setIsOpen(value.length >= 2);
  };

  const handleSelect = (value: string, option: any) => {
    const selectedOption = options.find(opt => opt.key === option.key);
    if (selectedOption) {
      handleEventSelect(selectedOption.event);
    }
  };

  const dropdownRender = () => {
    if (isLoading) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <Spin size="small" />
          <Text type="secondary" style={{ marginLeft: 8 }}>
            Searching...
          </Text>
        </div>
      );
    }

    if (searchValue.length < 2) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <Text type="secondary">Type at least 2 characters to search</Text>
        </div>
      );
    }

    if (options.length === 0) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No events found"
            style={{ margin: 0 }}
          />
        </div>
      );
    }

    return (
      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {options.map((option) => renderOption(option))}
        {options.length >= 10 && (
          <div style={{ padding: '12px 16px', textAlign: 'center', borderTop: '1px solid #f0f0f0' }}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                navigate(`/events?search=${encodeURIComponent(searchValue)}`);
                setSearchValue('');
                setIsOpen(false);
              }}
            >
              View all results for "{searchValue}"
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <AutoComplete
      ref={searchRef}
      value={searchValue}
      options={[]}
      onSearch={handleSearch}
      onSelect={handleSelect}
      open={isOpen}
      onDropdownVisibleChange={setIsOpen}
      dropdownRender={dropdownRender}
      style={style}
    >
      <Input
        size={size}
        placeholder={placeholder}
        prefix={<SearchOutlined />}
        allowClear
        onFocus={() => {
          if (searchValue.length >= 2) {
            setIsOpen(true);
          }
        }}
        onBlur={() => {
          // Delay closing to allow for option selection
          setTimeout(() => setIsOpen(false), 200);
        }}
      />
    </AutoComplete>
  );
};

export default GlobalSearch;
