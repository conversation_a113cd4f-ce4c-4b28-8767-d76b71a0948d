import { z } from "zod";

// Event priority levels
export enum EventPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
  CRITICAL = "critical",
}

// Event status
export enum EventStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  CONFIRMED = "confirmed",
  PENDING = "pending",
  CANCELLED = "cancelled",
  COMPLETED = "completed",
  POSTPONED = "postponed",
}

// Event types/categories
export enum EventType {
  MEETING = "meeting",
  WORKSHOP = "workshop",
  CONFERENCE = "conference",
  TRAINING = "training",
  SOCIAL = "social",
  ANNOUNCEMENT = "announcement",
  SEMINAR = "seminar",
  STRATEGIC_PLANNING = "strategic_planning",
  BOARD_MEETING = "board_meeting",
  TEAM_BUILDING = "team_building",
  PRESENTATION = "presentation",
  REVIEW = "review",
  OTHER = "other",
}

// Department categories
export enum Department {
  EXECUTIVE = "executive",
  FINANCE = "finance",
  OPERATIONS = "operations",
  HUMAN_RESOURCES = "human_resources",
  MARKETING = "marketing",
  SALES = "sales",
  TECHNOLOGY = "technology",
  LEGAL = "legal",
  COMPLIANCE = "compliance",
  STRATEGY = "strategy",
  RESEARCH = "research",
  CUSTOMER_SERVICE = "customer_service",
  ADMINISTRATION = "administration",
  OTHER = "other",
}

// Registration status
export enum RegistrationStatus {
  OPEN = "open",
  CLOSED = "closed",
  WAITLIST = "waitlist",
  FULL = "full",
}

// Zod schemas for validation
export const EventSchema = z
  .object({
    _id: z.string().optional(),
    title: z.string().min(1, "Title is required").max(200, "Title too long"),
    description: z.string().optional(),
    agenda: z.string().optional(),
    startDate: z.date(),
    endDate: z.date(),
    location: z.string().optional(),
    venue: z.string().optional(),
    spoc: z.string().optional(), // Single Point of Contact
    contactEmail: z.string().email().optional(),
    contactPhone: z.string().optional(),
    department: z.nativeEnum(Department).default(Department.OTHER),
    type: z.nativeEnum(EventType).default(EventType.OTHER),
    priority: z.nativeEnum(EventPriority).default(EventPriority.MEDIUM),
    status: z.nativeEnum(EventStatus).default(EventStatus.DRAFT),
    maxAttendees: z.number().positive().optional(),
    currentAttendees: z.number().min(0).default(0),
    registrationRequired: z.boolean().default(false),
    registrationStatus: z
      .nativeEnum(RegistrationStatus)
      .default(RegistrationStatus.OPEN),
    registrationDeadline: z.date().optional(),
    registrationLink: z.string().url().optional(),
    waitlistEnabled: z.boolean().default(false),
    tags: z.array(z.string()).default([]),
    attachments: z
      .array(
        z.object({
          name: z.string().min(1, "Attachment name is required"),
          url: z.string().url("Invalid attachment URL"),
          type: z.string().min(1, "Attachment type is required"),
          size: z.number().positive().optional(),
        })
      )
      .default([]),
    createdBy: z.string(),
    createdAt: z.date().default(() => new Date()),
    updatedAt: z.date().default(() => new Date()),
    isRecurring: z.boolean().default(false),
    recurrencePattern: z
      .object({
        frequency: z.enum(["daily", "weekly", "monthly", "yearly"]),
        interval: z.number().positive(),
        endDate: z.date().optional(),
        daysOfWeek: z.array(z.number().min(0).max(6)).optional(),
      })
      .optional(),
  })
  .refine(
    (data) => {
      // End date must be after start date
      return data.endDate >= data.startDate;
    },
    {
      message: "End date must be after start date",
      path: ["endDate"],
    }
  )
  .refine(
    (data) => {
      // Registration deadline must be before start date if registration is required
      if (data.registrationRequired && data.registrationDeadline) {
        return data.registrationDeadline <= data.startDate;
      }
      return true;
    },
    {
      message: "Registration deadline must be before event start date",
      path: ["registrationDeadline"],
    }
  )
  .refine(
    (data) => {
      // Current attendees cannot exceed max attendees
      if (data.maxAttendees && data.currentAttendees) {
        return data.currentAttendees <= data.maxAttendees;
      }
      return true;
    },
    {
      message: "Current attendees cannot exceed maximum capacity",
      path: ["currentAttendees"],
    }
  );

export const CreateEventSchema = EventSchema.omit({
  _id: true,
  createdAt: true,
  updatedAt: true,
  currentAttendees: true, // This will be managed by the system
});

export const UpdateEventSchema = EventSchema.partial().omit({
  _id: true,
  createdBy: true,
  createdAt: true,
});

// TypeScript types derived from Zod schemas
export type Event = z.infer<typeof EventSchema>;
export type CreateEventRequest = z.infer<typeof CreateEventSchema>;
export type UpdateEventRequest = z.infer<typeof UpdateEventSchema>;

// Event filter and search types
export interface EventFilters {
  startDate?: Date;
  endDate?: Date;
  type?: EventType[];
  status?: EventStatus[];
  priority?: EventPriority[];
  department?: Department[];
  registrationStatus?: RegistrationStatus[];
  tags?: string[];
  search?: string;
  createdBy?: string;
  location?: string;
  registrationRequired?: boolean;
  hasAvailableSpots?: boolean;
}

export interface EventSearchParams {
  page?: number;
  limit?: number;
  sortBy?: "startDate" | "createdAt" | "title" | "priority";
  sortOrder?: "asc" | "desc";
  filters?: EventFilters;
}

// API Response types
export interface PaginatedEventsResponse {
  events: Event[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Registration related types
export interface EventRegistration {
  _id?: string;
  eventId: string;
  userId: string;
  userEmail: string;
  userName: string;
  registrationDate: Date;
  status: "confirmed" | "waitlist" | "cancelled";
  notes?: string;
}

export const EventRegistrationSchema = z.object({
  _id: z.string().optional(),
  eventId: z.string().min(1, "Event ID is required"),
  userId: z.string().min(1, "User ID is required"),
  userEmail: z.string().email("Valid email is required"),
  userName: z.string().min(1, "User name is required"),
  registrationDate: z.date().default(() => new Date()),
  status: z.enum(["confirmed", "waitlist", "cancelled"]).default("confirmed"),
  notes: z.string().optional(),
});

export type CreateRegistrationRequest = z.infer<typeof EventRegistrationSchema>;

// Event statistics
export interface EventStats {
  totalEvents: number;
  upcomingEvents: number;
  completedEvents: number;
  cancelledEvents: number;
  eventsByType: Record<EventType, number>;
  eventsByDepartment: Record<Department, number>;
  eventsByStatus: Record<EventStatus, number>;
  registrationStats: {
    totalRegistrations: number;
    averageAttendance: number;
    popularEvents: Array<{
      eventId: string;
      title: string;
      registrations: number;
    }>;
  };
}
