import React from 'react';
import {
  Select,
  <PERSON>,
  Button,
  Typography,
  Tooltip,
} from 'antd';
import {
  SortAscendingOutlined,
  SortDescendingOutlined,
  SwapOutlined,
} from '@ant-design/icons';

const { Option } = Select;
const { Text } = Typography;

export interface SortOption {
  field: 'startDate' | 'createdAt' | 'title' | 'priority' | 'department' | 'type' | 'status';
  order: 'asc' | 'desc';
}

interface EventSortingProps {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  loading?: boolean;
}

const EventSorting: React.FC<EventSortingProps> = ({
  sortBy,
  sortOrder,
  onSortChange,
  loading = false,
}) => {
  const sortOptions = [
    {
      value: 'startDate',
      label: 'Event Date',
      description: 'Sort by when the event starts',
    },
    {
      value: 'createdAt',
      label: 'Created Date',
      description: 'Sort by when the event was created',
    },
    {
      value: 'title',
      label: 'Title',
      description: 'Sort alphabetically by event title',
    },
    {
      value: 'priority',
      label: 'Priority',
      description: 'Sort by event priority level',
    },
    {
      value: 'department',
      label: 'Department',
      description: 'Sort by organizing department',
    },
    {
      value: 'type',
      label: 'Event Type',
      description: 'Sort by type of event',
    },
    {
      value: 'status',
      label: 'Status',
      description: 'Sort by event status',
    },
  ];

  const handleSortFieldChange = (value: string) => {
    onSortChange(value, sortOrder);
  };

  const handleSortOrderToggle = () => {
    const newOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    onSortChange(sortBy, newOrder);
  };

  const getSortOrderIcon = () => {
    return sortOrder === 'asc' ? <SortAscendingOutlined /> : <SortDescendingOutlined />;
  };

  const getSortOrderText = () => {
    return sortOrder === 'asc' ? 'Ascending' : 'Descending';
  };

  const getFieldDisplayName = (field: string) => {
    const option = sortOptions.find(opt => opt.value === field);
    return option?.label || field;
  };

  return (
    <Space size="middle" align="center">
      <Text strong>Sort by:</Text>
      
      <Select
        value={sortBy}
        onChange={handleSortFieldChange}
        style={{ minWidth: 140 }}
        loading={loading}
        placeholder="Select field"
      >
        {sortOptions.map(option => (
          <Option key={option.value} value={option.value}>
            <Tooltip title={option.description} placement="right">
              {option.label}
            </Tooltip>
          </Option>
        ))}
      </Select>

      <Tooltip title={`Click to change to ${sortOrder === 'asc' ? 'descending' : 'ascending'} order`}>
        <Button
          icon={getSortOrderIcon()}
          onClick={handleSortOrderToggle}
          loading={loading}
          type="text"
        >
          {getSortOrderText()}
        </Button>
      </Tooltip>

      <Tooltip title="Reset to default sorting (Event Date, Ascending)">
        <Button
          icon={<SwapOutlined />}
          onClick={() => onSortChange('startDate', 'asc')}
          type="text"
          disabled={sortBy === 'startDate' && sortOrder === 'asc'}
        >
          Reset
        </Button>
      </Tooltip>
    </Space>
  );
};

export default EventSorting;
