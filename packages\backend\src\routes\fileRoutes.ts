import { Router } from 'express';
import { fileController } from '../controllers/fileController';
import { fileService } from '../services/fileService';
import { 
  authenticate, 
  canCreateEvents, 
  canEditEvents 
} from '../middleware/auth';
import { 
  validateObjectId, 
  sanitizeInput 
} from '../middleware/validation';

const router = Router();

// Configure multer middleware
const upload = fileService.getMulterConfig();

// All file routes require authentication
router.use(authenticate);

// Upload files (authenticated users can upload)
router.post('/upload', 
  upload.array('files', 10), // Allow up to 10 files
  fileController.uploadFiles
);

// Validate file before upload
router.post('/validate',
  upload.single('file'),
  fileController.validateFile
);

// Get file information
router.get('/:filename/info',
  fileController.getFileInfo
);

// Generate secure download URL
router.get('/:filename/secure-url',
  fileController.generateSecureUrl
);

// Scan file for viruses
router.get('/:filename/scan',
  fileController.scanFile
);

// Delete file (editors and admins only)
router.delete('/:filename',
  canEditEvents,
  fileController.deleteFile
);

// Get upload statistics (editors and admins only)
router.get('/stats',
  canEditEvents,
  fileController.getUploadStats
);

// Cleanup orphaned files (admin only)
router.post('/cleanup',
  canCreateEvents, // Assuming admin role
  fileController.cleanupOrphanedFiles
);

export default router;
