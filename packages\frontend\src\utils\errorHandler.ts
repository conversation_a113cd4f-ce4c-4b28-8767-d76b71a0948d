import { message, notification } from 'antd';
import { AxiosError } from 'axios';

export interface ApiError {
  message: string;
  code?: string;
  field?: string;
  statusCode?: number;
  details?: any;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ErrorResponse {
  success: false;
  message: string;
  error?: string;
  errors?: ValidationError[];
  code?: string;
  statusCode?: number;
}

export class AppError extends Error {
  public statusCode: number;
  public code?: string;
  public field?: string;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code?: string,
    field?: string,
    details?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
    this.field = field;
    this.details = details;

    // Maintains proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}

export class ValidationError extends AppError {
  public validationErrors: ValidationError[];

  constructor(message: string, validationErrors: ValidationError[] = []) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
    this.validationErrors = validationErrors;
  }
}

export class NetworkError extends AppError {
  constructor(message: string = 'Network error occurred') {
    super(message, 0, 'NETWORK_ERROR');
    this.name = 'NetworkError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class ServerError extends AppError {
  constructor(message: string = 'Internal server error') {
    super(message, 500, 'SERVER_ERROR');
    this.name = 'ServerError';
  }
}

// Error parsing utilities
export const parseApiError = (error: any): ApiError => {
  // Handle Axios errors
  if (error.isAxiosError) {
    const axiosError = error as AxiosError<ErrorResponse>;
    
    if (axiosError.response) {
      const { data, status } = axiosError.response;
      return {
        message: data?.message || data?.error || 'An error occurred',
        code: data?.code,
        statusCode: status,
        details: data?.errors || data?.details,
      };
    }
    
    if (axiosError.request) {
      return {
        message: 'Network error - please check your connection',
        code: 'NETWORK_ERROR',
        statusCode: 0,
      };
    }
  }

  // Handle custom app errors
  if (error instanceof AppError) {
    return {
      message: error.message,
      code: error.code,
      field: error.field,
      statusCode: error.statusCode,
      details: error.details,
    };
  }

  // Handle generic errors
  if (error instanceof Error) {
    return {
      message: error.message,
      code: 'UNKNOWN_ERROR',
      statusCode: 500,
    };
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      message: error,
      code: 'UNKNOWN_ERROR',
      statusCode: 500,
    };
  }

  // Fallback
  return {
    message: 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
    statusCode: 500,
  };
};

// Error display utilities
export const showErrorMessage = (error: any, defaultMessage?: string) => {
  const apiError = parseApiError(error);
  const errorMessage = apiError.message || defaultMessage || 'An error occurred';
  
  message.error(errorMessage);
};

export const showErrorNotification = (
  error: any,
  title: string = 'Error',
  defaultMessage?: string
) => {
  const apiError = parseApiError(error);
  const errorMessage = apiError.message || defaultMessage || 'An error occurred';
  
  notification.error({
    message: title,
    description: errorMessage,
    duration: 5,
  });
};

export const showValidationErrors = (errors: ValidationError[]) => {
  if (errors.length === 1) {
    message.error(errors[0].message);
  } else {
    notification.error({
      message: 'Validation Errors',
      description: (
        <ul style={{ margin: 0, paddingLeft: 20 }}>
          {errors.map((error, index) => (
            <li key={index}>
              {error.field ? `${error.field}: ` : ''}{error.message}
            </li>
          ))}
        </ul>
      ),
      duration: 8,
    });
  }
};

// Error logging utilities
export const logError = (error: any, context?: string, additionalData?: any) => {
  const apiError = parseApiError(error);
  
  console.group(`🚨 Error${context ? ` in ${context}` : ''}`);
  console.error('Message:', apiError.message);
  console.error('Code:', apiError.code);
  console.error('Status:', apiError.statusCode);
  
  if (apiError.field) {
    console.error('Field:', apiError.field);
  }
  
  if (apiError.details) {
    console.error('Details:', apiError.details);
  }
  
  if (additionalData) {
    console.error('Additional Data:', additionalData);
  }
  
  if (error.stack) {
    console.error('Stack:', error.stack);
  }
  
  console.groupEnd();

  // In production, you might want to send this to an error reporting service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error reporting service
    // errorReportingService.captureException(error, { context, additionalData });
  }
};

// Retry utilities
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  backoff: number = 2
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      const apiError = parseApiError(error);
      
      // Don't retry for certain error types
      if (
        apiError.statusCode === 400 || // Bad Request
        apiError.statusCode === 401 || // Unauthorized
        apiError.statusCode === 403 || // Forbidden
        apiError.statusCode === 404 || // Not Found
        apiError.statusCode === 422    // Unprocessable Entity
      ) {
        throw error;
      }
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(backoff, attempt - 1)));
    }
  }
  
  throw lastError;
};

// Error boundary helpers
export const createErrorBoundaryFallback = (
  title: string = 'Something went wrong',
  description: string = 'An unexpected error occurred. Please try refreshing the page.',
  showRetry: boolean = true
) => {
  return (
    <div style={{ padding: '50px 20px', textAlign: 'center' }}>
      <h2>{title}</h2>
      <p>{description}</p>
      {showRetry && (
        <button onClick={() => window.location.reload()}>
          Retry
        </button>
      )}
    </div>
  );
};

// Global error handler setup
export const setupGlobalErrorHandlers = () => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    logError(event.reason, 'Unhandled Promise Rejection');
    
    // Prevent the default browser behavior
    event.preventDefault();
    
    // Show user-friendly error message
    showErrorNotification(
      event.reason,
      'Unexpected Error',
      'An unexpected error occurred. Please try again.'
    );
  });

  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    logError(event.error, 'Uncaught Error', {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
    
    // Show user-friendly error message
    showErrorNotification(
      event.error,
      'Unexpected Error',
      'An unexpected error occurred. Please try again.'
    );
  });
};

export default {
  AppError,
  ValidationError,
  NetworkError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ServerError,
  parseApiError,
  showErrorMessage,
  showErrorNotification,
  showValidationErrors,
  logError,
  withRetry,
  createErrorBoundaryFallback,
  setupGlobalErrorHandlers,
};
