import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Alert,
  Progress,
  Divider,
  Card,
  Row,
  Col,
  Tag,
  Tooltip,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { Event, RegistrationStatus } from '@gbf-calendar/shared';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface EventRegistrationProps {
  event: Event | null;
  visible: boolean;
  onClose: () => void;
  onRegister: (data: { userEmail?: string; userName?: string; notes?: string }) => void;
  loading?: boolean;
  userInfo?: {
    email: string;
    name: string;
  };
}

const EventRegistration: React.FC<EventRegistrationProps> = ({
  event,
  visible,
  onClose,
  onRegister,
  loading = false,
  userInfo,
}) => {
  const [form] = Form.useForm();
  const [notes, setNotes] = useState('');

  if (!event) return null;

  const getRegistrationProgress = () => {
    if (!event.maxAttendees) return 0;
    return Math.round((event.currentAttendees / event.maxAttendees) * 100);
  };

  const isRegistrationDeadlinePassed = () => {
    return event.registrationDeadline && moment().isAfter(moment(event.registrationDeadline));
  };

  const isEventFull = () => {
    return event.maxAttendees && event.currentAttendees >= event.maxAttendees;
  };

  const canRegister = () => {
    if (isRegistrationDeadlinePassed()) return false;
    if (event.registrationStatus === RegistrationStatus.CLOSED) return false;
    if (event.registrationStatus === RegistrationStatus.FULL && !event.waitlistEnabled) return false;
    return true;
  };

  const getRegistrationStatusInfo = () => {
    if (isRegistrationDeadlinePassed()) {
      return {
        type: 'error' as const,
        message: 'Registration Closed',
        description: 'The registration deadline has passed.',
      };
    }

    if (event.registrationStatus === RegistrationStatus.CLOSED) {
      return {
        type: 'error' as const,
        message: 'Registration Closed',
        description: 'Registration is no longer available for this event.',
      };
    }

    if (isEventFull()) {
      if (event.waitlistEnabled) {
        return {
          type: 'warning' as const,
          message: 'Event Full - Join Waitlist',
          description: 'This event is at capacity, but you can join the waitlist.',
        };
      } else {
        return {
          type: 'error' as const,
          message: 'Event Full',
          description: 'This event has reached maximum capacity and waitlist is not available.',
        };
      }
    }

    return {
      type: 'success' as const,
      message: 'Registration Open',
      description: 'You can register for this event.',
    };
  };

  const handleSubmit = (values: any) => {
    onRegister({
      userEmail: values.email || userInfo?.email,
      userName: values.name || userInfo?.name,
      notes: values.notes,
    });
  };

  const statusInfo = getRegistrationStatusInfo();
  const willJoinWaitlist = isEventFull() && event.waitlistEnabled;

  return (
    <Modal
      title={
        <Space>
          <TeamOutlined />
          <span>Event Registration</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Event Summary */}
        <Card size="small">
          <Title level={4} style={{ margin: 0, marginBottom: 8 }}>
            {event.title}
          </Title>
          <Space direction="vertical" size={4}>
            <Text>
              <ClockCircleOutlined /> {moment(event.startDate).format('dddd, MMMM DD, YYYY [at] HH:mm')}
            </Text>
            {event.location && (
              <Text>
                <UserOutlined /> {event.location}
              </Text>
            )}
            {event.spoc && (
              <Text>
                Contact: {event.spoc}
                {event.contactEmail && ` (${event.contactEmail})`}
              </Text>
            )}
          </Space>
        </Card>

        {/* Registration Status */}
        <Alert
          message={statusInfo.message}
          description={statusInfo.description}
          type={statusInfo.type}
          showIcon
          icon={
            statusInfo.type === 'success' ? <CheckCircleOutlined /> : 
            statusInfo.type === 'warning' ? <ExclamationCircleOutlined /> : 
            <ExclamationCircleOutlined />
          }
        />

        {/* Capacity Information */}
        {event.maxAttendees && (
          <Card size="small" title="Event Capacity">
            <Row gutter={16} align="middle">
              <Col span={16}>
                <Progress
                  percent={getRegistrationProgress()}
                  status={getRegistrationProgress() >= 100 ? 'exception' : 'active'}
                  format={(percent) => `${event.currentAttendees}/${event.maxAttendees}`}
                />
              </Col>
              <Col span={8}>
                <Space direction="vertical" size={0}>
                  <Text strong>{event.currentAttendees} registered</Text>
                  <Text type="secondary">
                    {event.maxAttendees - event.currentAttendees} spots left
                  </Text>
                </Space>
              </Col>
            </Row>
          </Card>
        )}

        {/* Registration Deadline */}
        {event.registrationDeadline && (
          <Card size="small">
            <Space>
              <ClockCircleOutlined />
              <Text strong>Registration Deadline:</Text>
              <Text>
                {moment(event.registrationDeadline).format('MMMM DD, YYYY [at] HH:mm')}
              </Text>
              <Tag color={isRegistrationDeadlinePassed() ? 'red' : 'green'}>
                {isRegistrationDeadlinePassed() ? 'Passed' : 'Open'}
              </Tag>
            </Space>
          </Card>
        )}

        {/* Registration Form */}
        {canRegister() && (
          <>
            <Divider>Registration Details</Divider>
            
            {willJoinWaitlist && (
              <Alert
                message="Joining Waitlist"
                description="Since the event is full, you will be added to the waitlist. You'll be notified if a spot becomes available."
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                email: userInfo?.email,
                name: userInfo?.name,
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Full Name"
                    name="name"
                    rules={[{ required: true, message: 'Please enter your full name' }]}
                  >
                    <Input
                      prefix={<UserOutlined />}
                      placeholder="Enter your full name"
                      disabled={!!userInfo?.name}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Email Address"
                    name="email"
                    rules={[
                      { required: true, message: 'Please enter your email' },
                      { type: 'email', message: 'Please enter a valid email' },
                    ]}
                  >
                    <Input
                      prefix={<MailOutlined />}
                      placeholder="Enter your email address"
                      disabled={!!userInfo?.email}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Additional Notes (Optional)"
                name="notes"
                help="Any special requirements, dietary restrictions, or comments"
              >
                <TextArea
                  rows={3}
                  placeholder="Enter any additional information..."
                  maxLength={500}
                  showCount
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 0 }}>
                <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                  <Button onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<TeamOutlined />}
                  >
                    {willJoinWaitlist ? 'Join Waitlist' : 'Register for Event'}
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        )}

        {/* External Registration Link */}
        {event.registrationLink && (
          <>
            <Divider>External Registration</Divider>
            <Card size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text>This event uses an external registration system.</Text>
                <Button
                  type="primary"
                  href={event.registrationLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  block
                >
                  Register on External Site
                </Button>
              </Space>
            </Card>
          </>
        )}

        {/* Action Buttons for Non-Registrable Events */}
        {!canRegister() && !event.registrationLink && (
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={onClose}>
              Close
            </Button>
          </Space>
        )}
      </Space>
    </Modal>
  );
};

export default EventRegistration;
