import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Button,
  Typography,
  Space,
  Modal,
  message,
  Spin,
} from "antd";
import { PlusOutlined, ReloadOutlined } from "@ant-design/icons";
import { Helmet } from "react-helmet-async";
import { config } from "@/utils/config";
import {
  useEvents,
  useCreateEvent,
  useUpdateEvent,
  useDeleteEvent,
} from "@/hooks/useEvents";
import {
  Event,
  EventFilters as IEventFilters,
  CreateEventRequest,
} from "@gbf-calendar/shared";
import EventList from "@/components/events/EventList";
import EventFilters from "@/components/events/EventFilters";
import EventForm from "@/components/events/EventForm";
import EventDetailModal from "@/components/events/EventDetailModal";
import EventSorting from "@/components/events/EventSorting";
import SavedSearches from "@/components/events/SavedSearches";
import GlobalSearch from "@/components/common/GlobalSearch";

const { Title } = Typography;

const EventsPage: React.FC = () => {
  const [filters, setFilters] = useState<IEventFilters>({});
  const [sortBy, setSortBy] = useState("startDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [eventToEdit, setEventToEdit] = useState<Event | null>(null);

  // API hooks
  const {
    data: eventsData,
    isLoading,
    refetch,
  } = useEvents({
    page: currentPage,
    limit: pageSize,
    sortBy: sortBy as any,
    sortOrder,
    filters,
  });

  const createEventMutation = useCreateEvent({
    onSuccess: () => {
      message.success("Event created successfully");
      setIsCreateModalOpen(false);
      refetch();
    },
    onError: (error: any) => {
      message.error(error.message || "Failed to create event");
    },
  });

  const updateEventMutation = useUpdateEvent({
    onSuccess: () => {
      message.success("Event updated successfully");
      setIsEditModalOpen(false);
      setEventToEdit(null);
      refetch();
    },
    onError: (error: any) => {
      message.error(error.message || "Failed to update event");
    },
  });

  const deleteEventMutation = useDeleteEvent({
    onSuccess: () => {
      message.success("Event deleted successfully");
      refetch();
    },
    onError: (error: any) => {
      message.error(error.message || "Failed to delete event");
    },
  });

  const handleFiltersChange = (newFilters: IEventFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  const handleCreateEvent = (data: CreateEventRequest) => {
    createEventMutation.mutate(data);
  };

  const handleUpdateEvent = (data: CreateEventRequest) => {
    if (eventToEdit) {
      updateEventMutation.mutate({
        id: eventToEdit._id!,
        data,
      });
    }
  };

  const handleDeleteEvent = (event: Event) => {
    Modal.confirm({
      title: "Delete Event",
      content: `Are you sure you want to delete "${event.title}"? This action cannot be undone.`,
      okText: "Delete",
      okType: "danger",
      cancelText: "Cancel",
      onOk: () => {
        if (event._id) {
          deleteEventMutation.mutate(event._id);
        }
      },
    });
  };

  const handleEditEvent = (event: Event) => {
    setEventToEdit(event);
    setIsEditModalOpen(true);
  };

  const handleViewEvent = (event: Event) => {
    setSelectedEvent(event);
    setIsDetailModalOpen(true);
  };

  const handleSortChange = (
    newSortBy: string,
    newSortOrder: "asc" | "desc"
  ) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCurrentPage(1); // Reset to first page when sorting changes
  };

  const handleLoadSavedSearch = (savedFilters: IEventFilters) => {
    setFilters(savedFilters);
    setCurrentPage(1);
  };

  const handleGlobalSearchSelect = (event: Event) => {
    setSelectedEvent(event);
    setIsDetailModalOpen(true);
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <>
      <Helmet>
        <title>Events - {config.appName}</title>
        <meta name="description" content="Manage and browse all events" />
      </Helmet>

      <div style={{ padding: "24px" }}>
        {/* Header */}
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: 24 }}
        >
          <Col>
            <Title level={2} style={{ margin: 0 }}>
              Events Management
            </Title>
          </Col>
          <Col flex="auto" style={{ maxWidth: 400, margin: "0 24px" }}>
            <GlobalSearch
              placeholder="Quick search events..."
              onEventSelect={handleGlobalSearchSelect}
              style={{ width: "100%" }}
            />
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={isLoading}
              >
                Refresh
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsCreateModalOpen(true)}
              >
                Create Event
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Sorting Controls */}
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: 16 }}
        >
          <Col>
            <EventSorting
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              loading={isLoading}
            />
          </Col>
          <Col>
            <Space>
              <span>
                {eventsData?.total || 0} event
                {(eventsData?.total || 0) !== 1 ? "s" : ""} found
              </span>
            </Space>
          </Col>
        </Row>

        {/* Filters and Content */}
        <Row gutter={[24, 24]}>
          {/* Filters Sidebar */}
          <Col xs={24} lg={6}>
            <Space direction="vertical" style={{ width: "100%" }} size="middle">
              <SavedSearches
                currentFilters={filters}
                onLoadSearch={handleLoadSavedSearch}
              />
              <EventFilters
                onFiltersChange={handleFiltersChange}
                loading={isLoading}
                initialFilters={filters}
              />
            </Space>
          </Col>

          {/* Events List */}
          <Col xs={24} lg={18}>
            <EventList
              events={eventsData?.events || []}
              loading={isLoading}
              total={eventsData?.total || 0}
              current={currentPage}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onEventClick={handleViewEvent}
              onEditEvent={handleEditEvent}
              onDeleteEvent={handleDeleteEvent}
              onViewEvent={handleViewEvent}
              showActions={true}
              userRole="admin" // This should come from auth context
            />
          </Col>
        </Row>

        {/* Create Event Modal */}
        <Modal
          title="Create New Event"
          open={isCreateModalOpen}
          onCancel={() => setIsCreateModalOpen(false)}
          footer={null}
          width={1000}
          destroyOnClose
        >
          <EventForm
            onSubmit={handleCreateEvent}
            onCancel={() => setIsCreateModalOpen(false)}
            loading={createEventMutation.isPending}
            mode="create"
          />
        </Modal>

        {/* Edit Event Modal */}
        <Modal
          title="Edit Event"
          open={isEditModalOpen}
          onCancel={() => {
            setIsEditModalOpen(false);
            setEventToEdit(null);
          }}
          footer={null}
          width={1000}
          destroyOnClose
        >
          {eventToEdit && (
            <EventForm
              initialData={eventToEdit}
              onSubmit={handleUpdateEvent}
              onCancel={() => {
                setIsEditModalOpen(false);
                setEventToEdit(null);
              }}
              loading={updateEventMutation.isPending}
              mode="edit"
            />
          )}
        </Modal>

        {/* Event Detail Modal */}
        <EventDetailModal
          event={selectedEvent}
          visible={isDetailModalOpen}
          onClose={() => {
            setIsDetailModalOpen(false);
            setSelectedEvent(null);
          }}
          onEdit={handleEditEvent}
          onDelete={handleDeleteEvent}
          userRole="admin" // This should come from auth context
          isRegistered={false} // This should come from registration status
          loading={false}
        />
      </div>
    </>
  );
};

export default EventsPage;
