import React, { useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Button,
  List,
  Avatar,
  Tag,
  Progress,
  Calendar,
  Badge,
  Timeline,
  Alert,
  Tooltip,
  Divider,
} from 'antd';
import {
  CalendarOutlined,
  TeamOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  FallOutlined,
  UserOutlined,
  EnvironmentOutlined,
  PlusOutlined,
  EyeOutlined,
  BellOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { Event, EventStatus, EventPriority, Department, EventStats } from '@gbf-calendar/shared';

const { Title, Text } = Typography;

interface EventsDashboardProps {
  events: Event[];
  stats: EventStats;
  userRegistrations?: Event[];
  loading?: boolean;
  onCreateEvent?: () => void;
  onViewEvent?: (event: Event) => void;
  onViewAllEvents?: () => void;
  onViewDepartments?: () => void;
}

const EventsDashboard: React.FC<EventsDashboardProps> = ({
  events,
  stats,
  userRegistrations = [],
  loading = false,
  onCreateEvent,
  onViewEvent,
  onViewAllEvents,
  onViewDepartments,
}) => {
  const [selectedDate, setSelectedDate] = useState<moment.Moment>(moment());

  const getDepartmentColor = (department: Department) => {
    const colors = {
      [Department.EXECUTIVE]: 'purple',
      [Department.FINANCE]: 'green',
      [Department.OPERATIONS]: 'blue',
      [Department.HUMAN_RESOURCES]: 'orange',
      [Department.MARKETING]: 'pink',
      [Department.SALES]: 'cyan',
      [Department.TECHNOLOGY]: 'geekblue',
      [Department.LEGAL]: 'red',
      [Department.COMPLIANCE]: 'yellow',
      [Department.STRATEGY]: 'magenta',
      [Department.RESEARCH]: 'lime',
      [Department.CUSTOMER_SERVICE]: 'volcano',
      [Department.ADMINISTRATION]: 'gold',
      [Department.OTHER]: 'default',
    };
    return colors[department] || 'default';
  };

  const getStatusColor = (status: EventStatus) => {
    const colors = {
      [EventStatus.DRAFT]: 'default',
      [EventStatus.PUBLISHED]: 'blue',
      [EventStatus.CONFIRMED]: 'green',
      [EventStatus.PENDING]: 'orange',
      [EventStatus.CANCELLED]: 'red',
      [EventStatus.COMPLETED]: 'purple',
      [EventStatus.POSTPONED]: 'yellow',
    };
    return colors[status] || 'default';
  };

  const formatDepartmentName = (department: Department) => {
    return department.replace(/_/g, ' ').toUpperCase();
  };

  const isEventToday = (event: Event) => {
    return moment(event.startDate).isSame(moment(), 'day');
  };

  const isEventUpcoming = (event: Event) => {
    return moment(event.startDate).isAfter(moment());
  };

  const isEventOngoing = (event: Event) => {
    const now = moment();
    return now.isBetween(moment(event.startDate), moment(event.endDate));
  };

  const todaysEvents = events.filter(isEventToday);
  const upcomingEvents = events
    .filter(isEventUpcoming)
    .sort((a, b) => moment(a.startDate).unix() - moment(b.startDate).unix())
    .slice(0, 5);
  const ongoingEvents = events.filter(isEventOngoing);

  const getUpcomingRegistrations = () => {
    return userRegistrations
      .filter(event => isEventUpcoming(event))
      .sort((a, b) => moment(a.startDate).unix() - moment(b.startDate).unix())
      .slice(0, 3);
  };

  const getCalendarData = (date: moment.Moment) => {
    const dayEvents = events.filter(event =>
      moment(event.startDate).isSame(date, 'day')
    );
    
    return dayEvents.map(event => ({
      type: getStatusColor(event.status) as any,
      content: event.title,
    }));
  };

  const renderQuickStats = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="Total Events"
            value={stats.totalEvents}
            prefix={<CalendarOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
          <div style={{ marginTop: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {stats.upcomingEvents} upcoming
            </Text>
          </div>
        </Card>
      </Col>
      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="My Registrations"
            value={userRegistrations.length}
            prefix={<TeamOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
          <div style={{ marginTop: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {getUpcomingRegistrations().length} upcoming
            </Text>
          </div>
        </Card>
      </Col>
      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="Today's Events"
            value={todaysEvents.length}
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: '#fa8c16' }}
          />
          <div style={{ marginTop: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {ongoingEvents.length} ongoing
            </Text>
          </div>
        </Card>
      </Col>
      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="Completed"
            value={stats.completedEvents}
            prefix={<TrophyOutlined />}
            valueStyle={{ color: '#722ed1' }}
          />
          <div style={{ marginTop: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              This month
            </Text>
          </div>
        </Card>
      </Col>
    </Row>
  );

  const renderTodaysEvents = () => (
    <Card
      title={
        <Space>
          <ClockCircleOutlined />
          <span>Today's Events</span>
          <Badge count={todaysEvents.length} showZero />
        </Space>
      }
      extra={
        <Button type="link" onClick={onViewAllEvents}>
          View All
        </Button>
      }
    >
      {todaysEvents.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">No events scheduled for today</Text>
        </div>
      ) : (
        <List
          dataSource={todaysEvents}
          renderItem={(event) => (
            <List.Item
              actions={[
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => onViewEvent?.(event)}
                >
                  View
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    style={{
                      backgroundColor: getDepartmentColor(event.department),
                      color: '#fff',
                    }}
                    size="small"
                  >
                    {event.department.charAt(0).toUpperCase()}
                  </Avatar>
                }
                title={
                  <Space>
                    <Text strong>{event.title}</Text>
                    {isEventOngoing(event) && (
                      <Badge status="processing" text="Live" />
                    )}
                  </Space>
                }
                description={
                  <Space wrap>
                    <Text type="secondary">
                      {moment(event.startDate).format('HH:mm')} - {moment(event.endDate).format('HH:mm')}
                    </Text>
                    {event.location && (
                      <Text type="secondary">📍 {event.location}</Text>
                    )}
                    <Tag color={getStatusColor(event.status)} size="small">
                      {event.status.toUpperCase()}
                    </Tag>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );

  const renderUpcomingEvents = () => (
    <Card
      title={
        <Space>
          <CalendarOutlined />
          <span>Upcoming Events</span>
        </Space>
      }
      extra={
        <Button type="link" onClick={onViewAllEvents}>
          View All
        </Button>
      }
    >
      {upcomingEvents.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">No upcoming events</Text>
        </div>
      ) : (
        <Timeline>
          {upcomingEvents.map((event) => (
            <Timeline.Item
              key={event._id}
              color={getDepartmentColor(event.department)}
              label={moment(event.startDate).format('MMM DD')}
            >
              <Card
                size="small"
                style={{ cursor: 'pointer' }}
                onClick={() => onViewEvent?.(event)}
              >
                <Space direction="vertical" size={4}>
                  <Space>
                    <Text strong>{event.title}</Text>
                    <Tag color={getStatusColor(event.status)} size="small">
                      {event.status.toUpperCase()}
                    </Tag>
                  </Space>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {moment(event.startDate).format('dddd, HH:mm')}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {formatDepartmentName(event.department)}
                  </Text>
                </Space>
              </Card>
            </Timeline.Item>
          ))}
        </Timeline>
      )}
    </Card>
  );

  const renderMyRegistrations = () => (
    <Card
      title={
        <Space>
          <UserOutlined />
          <span>My Upcoming Events</span>
        </Space>
      }
    >
      {getUpcomingRegistrations().length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">No upcoming registrations</Text>
        </div>
      ) : (
        <List
          dataSource={getUpcomingRegistrations()}
          renderItem={(event) => (
            <List.Item>
              <List.Item.Meta
                title={
                  <Space>
                    <Text strong>{event.title}</Text>
                    <Badge status="success" text="Registered" />
                  </Space>
                }
                description={
                  <Space direction="vertical" size={2}>
                    <Text type="secondary">
                      {moment(event.startDate).format('MMM DD, YYYY [at] HH:mm')}
                    </Text>
                    {event.location && (
                      <Text type="secondary">📍 {event.location}</Text>
                    )}
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );

  const renderQuickActions = () => (
    <Card title="Quick Actions">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={onCreateEvent}
          block
        >
          Create New Event
        </Button>
        <Button
          icon={<CalendarOutlined />}
          onClick={onViewAllEvents}
          block
        >
          Browse All Events
        </Button>
        <Button
          icon={<TeamOutlined />}
          onClick={onViewDepartments}
          block
        >
          Department View
        </Button>
      </Space>
    </Card>
  );

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      {/* Header */}
      <Row justify="space-between" align="middle">
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            Events Dashboard
          </Title>
          <Text type="secondary">
            Welcome back! Here's what's happening with your events.
          </Text>
        </Col>
        <Col>
          <Button type="primary" icon={<PlusOutlined />} onClick={onCreateEvent}>
            Create Event
          </Button>
        </Col>
      </Row>

      {/* Quick Stats */}
      {renderQuickStats()}

      {/* Main Content */}
      <Row gutter={[16, 16]}>
        {/* Left Column */}
        <Col xs={24} lg={16}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* Today's Events */}
            {renderTodaysEvents()}

            {/* Upcoming Events */}
            {renderUpcomingEvents()}
          </Space>
        </Col>

        {/* Right Column */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* My Registrations */}
            {renderMyRegistrations()}

            {/* Quick Actions */}
            {renderQuickActions()}

            {/* Mini Calendar */}
            <Card title="Calendar" size="small">
              <Calendar
                fullscreen={false}
                value={selectedDate}
                onSelect={setSelectedDate}
                dateCellRender={(date) => {
                  const dayEvents = events.filter(event =>
                    moment(event.startDate).isSame(date, 'day')
                  );
                  
                  return (
                    <div>
                      {dayEvents.slice(0, 2).map(event => (
                        <Badge
                          key={event._id}
                          status={getStatusColor(event.status) as any}
                          text=""
                          style={{ display: 'block', fontSize: '8px' }}
                        />
                      ))}
                      {dayEvents.length > 2 && (
                        <Text style={{ fontSize: '8px' }}>+{dayEvents.length - 2}</Text>
                      )}
                    </div>
                  );
                }}
              />
            </Card>
          </Space>
        </Col>
      </Row>
    </Space>
  );
};

export default EventsDashboard;
