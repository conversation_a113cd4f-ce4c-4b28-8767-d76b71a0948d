import { Request, Response, NextFunction } from "express";
import { validation<PERSON><PERSON><PERSON>, Val<PERSON><PERSON><PERSON><PERSON><PERSON> } from "express-validator";
import { z } from "zod";
import DOMPurify from "isomorphic-dompurify";
import rateLimit from "express-rate-limit";
import { logger } from "../utils/logger";
import { CustomError } from "./errorHandler";

// Extend Request interface to include file property from multer
declare global {
  namespace Express {
    interface Request {
      file?: {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        size: number;
        destination: string;
        filename: string;
        path: string;
        buffer: Buffer;
      };
      files?:
        | {
            [fieldname: string]: Express.Multer.File[];
          }
        | Express.Multer.File[];
    }
  }
}

// Security constants
const MAX_STRING_LENGTH = 10000;
const MAX_ARRAY_LENGTH = 100;
const DANGEROUS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /data:text\/html/gi,
  /vbscript:/gi,
];

// SQL injection patterns
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
  /(--|\/\*|\*\/|;|'|"|`)/g,
  /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
];

// XSS patterns
const XSS_PATTERNS = [
  /<[^>]*>/g,
  /&[#\w]+;/g,
  /javascript:/gi,
  /vbscript:/gi,
  /on\w+\s*=/gi,
];

// Middleware to handle express-validator results
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const formattedErrors: Record<string, string[]> = {};

    errors.array().forEach((error) => {
      const field = error.type === "field" ? error.path : "general";
      if (!formattedErrors[field]) {
        formattedErrors[field] = [];
      }
      formattedErrors[field].push(error.msg);
    });

    res.status(400).json({
      success: false,
      error: "Validation failed",
      errors: formattedErrors,
    });
    return;
  }

  next();
};

// Generic Zod validation middleware
export const validateSchema = <T>(
  schema: z.ZodSchema<T>,
  property: "body" | "query" | "params" = "body"
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const data = req[property];
      const validatedData = schema.parse(data);

      // Replace the original data with validated data
      req[property] = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors: Record<string, string[]> = {};

        error.errors.forEach((err) => {
          const path = err.path.join(".");
          if (!formattedErrors[path]) {
            formattedErrors[path] = [];
          }
          formattedErrors[path].push(err.message);
        });

        res.status(400).json({
          success: false,
          error: "Validation failed",
          errors: formattedErrors,
        });
        return;
      }

      logger.error("Validation middleware error", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  };
};

// Middleware to validate pagination parameters
export const validatePagination = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const { page, limit } = req.query;

  // Validate page
  if (page !== undefined) {
    const pageNum = parseInt(page as string, 10);
    if (isNaN(pageNum) || pageNum < 1) {
      res.status(400).json({
        success: false,
        error: "Page must be a positive integer",
      });
      return;
    }
    req.query.page = pageNum.toString();
  }

  // Validate limit
  if (limit !== undefined) {
    const limitNum = parseInt(limit as string, 10);
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      res.status(400).json({
        success: false,
        error: "Limit must be between 1 and 100",
      });
      return;
    }
    req.query.limit = limitNum.toString();
  }

  next();
};

// Middleware to validate date range parameters
export const validateDateRange = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const { startDate, endDate } = req.query;

  if (startDate) {
    const start = new Date(startDate as string);
    if (isNaN(start.getTime())) {
      res.status(400).json({
        success: false,
        error: "Invalid start date format",
      });
      return;
    }
    req.query.startDate = start.toISOString();
  }

  if (endDate) {
    const end = new Date(endDate as string);
    if (isNaN(end.getTime())) {
      res.status(400).json({
        success: false,
        error: "Invalid end date format",
      });
      return;
    }
    req.query.endDate = end.toISOString();
  }

  // Validate that start date is before end date
  if (startDate && endDate) {
    const start = new Date(req.query.startDate as string);
    const end = new Date(req.query.endDate as string);

    if (start >= end) {
      res.status(400).json({
        success: false,
        error: "Start date must be before end date",
      });
      return;
    }
  }

  next();
};

// Middleware to validate MongoDB ObjectId
export const validateObjectId = (paramName: string = "id") => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const id = req.params[paramName];

    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      res.status(400).json({
        success: false,
        error: `Invalid ${paramName} format`,
      });
      return;
    }

    next();
  };
};

// Enhanced security validation functions
const detectSQLInjection = (input: string): boolean => {
  return SQL_INJECTION_PATTERNS.some((pattern) => pattern.test(input));
};

const detectXSS = (input: string): boolean => {
  return XSS_PATTERNS.some((pattern) => pattern.test(input));
};

const detectDangerousContent = (input: string): boolean => {
  return DANGEROUS_PATTERNS.some((pattern) => pattern.test(input));
};

const validateStringLength = (
  input: string,
  maxLength: number = MAX_STRING_LENGTH
): boolean => {
  return input.length <= maxLength;
};

const validateArrayLength = (
  input: any[],
  maxLength: number = MAX_ARRAY_LENGTH
): boolean => {
  return input.length <= maxLength;
};

// Enhanced sanitization function
const sanitizeString = (input: string): string => {
  if (!input || typeof input !== "string") return input;

  // Remove dangerous patterns
  let sanitized = input;
  DANGEROUS_PATTERNS.forEach((pattern) => {
    sanitized = sanitized.replace(pattern, "");
  });

  // Use DOMPurify for additional XSS protection
  sanitized = DOMPurify.sanitize(sanitized, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
  });

  // Trim and normalize whitespace
  sanitized = sanitized.trim().replace(/\s+/g, " ");

  return sanitized;
};

// Security validation middleware
export const securityValidation = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const validateValue = (value: any, path: string = ""): void => {
    if (typeof value === "string") {
      // Check string length
      if (!validateStringLength(value)) {
        throw new CustomError(`String too long at ${path}`, 400);
      }

      // Check for SQL injection
      if (detectSQLInjection(value)) {
        logger.warn("SQL injection attempt detected", {
          path,
          value: value.substring(0, 100),
          ip: req.ip,
          userAgent: req.get("User-Agent"),
        });
        throw new CustomError("Invalid input detected", 400);
      }

      // Check for XSS
      if (detectXSS(value)) {
        logger.warn("XSS attempt detected", {
          path,
          value: value.substring(0, 100),
          ip: req.ip,
          userAgent: req.get("User-Agent"),
        });
        throw new CustomError("Invalid input detected", 400);
      }

      // Check for dangerous content
      if (detectDangerousContent(value)) {
        logger.warn("Dangerous content detected", {
          path,
          value: value.substring(0, 100),
          ip: req.ip,
          userAgent: req.get("User-Agent"),
        });
        throw new CustomError("Invalid input detected", 400);
      }
    } else if (Array.isArray(value)) {
      // Check array length
      if (!validateArrayLength(value)) {
        throw new CustomError(`Array too long at ${path}`, 400);
      }

      // Validate each array element
      value.forEach((item, index) => {
        validateValue(item, `${path}[${index}]`);
      });
    } else if (value && typeof value === "object") {
      // Validate object properties
      Object.keys(value).forEach((key) => {
        validateValue(value[key], path ? `${path}.${key}` : key);
      });
    }
  };

  try {
    // Validate request body
    if (req.body) {
      validateValue(req.body, "body");
    }

    // Validate query parameters
    if (req.query) {
      validateValue(req.query, "query");
    }

    // Validate URL parameters
    if (req.params) {
      validateValue(req.params, "params");
    }

    next();
  } catch (error) {
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        success: false,
        error: error.message,
      });
      return;
    }

    logger.error("Security validation error", error);
    res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
};

// Enhanced input sanitization middleware
export const sanitizeInput = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === "string") {
      return sanitizeString(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }

    if (obj && typeof obj === "object") {
      const sanitized: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          sanitized[key] = sanitizeObject(obj[key]);
        }
      }
      return sanitized;
    }

    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }

  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};

// Middleware to validate file upload
export const validateFileUpload = (allowedTypes: string[], maxSize: number) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.file) {
      res.status(400).json({
        success: false,
        error: "No file uploaded",
      });
      return;
    }

    // Check file type
    if (!allowedTypes.includes(req.file.mimetype)) {
      res.status(400).json({
        success: false,
        error: `File type not allowed. Allowed types: ${allowedTypes.join(
          ", "
        )}`,
      });
      return;
    }

    // Check file size
    if (req.file.size > maxSize) {
      res.status(400).json({
        success: false,
        error: `File size too large. Maximum size: ${
          maxSize / (1024 * 1024)
        }MB`,
      });
      return;
    }

    next();
  };
};
