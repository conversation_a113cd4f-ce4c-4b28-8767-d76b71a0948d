import { Request, Response } from 'express';
import { fileService } from '../services/fileService';
import { asyncHandler } from '../middleware/asyncHandler';
import { CustomError } from '../utils/errors';
import { logger } from '../utils/logger';

export class FileController {
  // Upload files
  uploadFiles = asyncHandler(async (req: Request, res: Response) => {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      throw new CustomError('No files uploaded', 400);
    }

    const uploadedFiles = await fileService.processUploadedFiles(req.files);

    res.status(200).json({
      success: true,
      data: uploadedFiles,
      message: `${uploadedFiles.length} file(s) uploaded successfully`,
    });
  });

  // Delete a file
  deleteFile = asyncHandler(async (req: Request, res: Response) => {
    const { filename } = req.params;

    if (!filename) {
      throw new CustomError('Filename is required', 400);
    }

    await fileService.deleteFile(filename);

    res.status(200).json({
      success: true,
      message: 'File deleted successfully',
    });
  });

  // Get file information
  getFileInfo = asyncHandler(async (req: Request, res: Response) => {
    const { filename } = req.params;

    if (!filename) {
      throw new CustomError('Filename is required', 400);
    }

    const fileInfo = await fileService.getFileInfo(filename);

    if (!fileInfo.exists) {
      throw new CustomError('File not found', 404);
    }

    res.status(200).json({
      success: true,
      data: fileInfo,
      message: 'File information retrieved successfully',
    });
  });

  // Get upload statistics
  getUploadStats = asyncHandler(async (req: Request, res: Response) => {
    const stats = await fileService.getUploadStats();

    res.status(200).json({
      success: true,
      data: stats,
      message: 'Upload statistics retrieved successfully',
    });
  });

  // Cleanup orphaned files (admin only)
  cleanupOrphanedFiles = asyncHandler(async (req: Request, res: Response) => {
    const result = await fileService.cleanupOrphanedFiles();

    res.status(200).json({
      success: true,
      data: result,
      message: `Cleanup completed. ${result.deletedCount} files deleted.`,
    });
  });

  // Validate file before upload
  validateFile = asyncHandler(async (req: Request, res: Response) => {
    if (!req.file) {
      throw new CustomError('No file provided for validation', 400);
    }

    const validation = fileService.validateFile(req.file);

    res.status(200).json({
      success: true,
      data: validation,
      message: validation.valid ? 'File is valid' : 'File validation failed',
    });
  });

  // Generate secure download URL
  generateSecureUrl = asyncHandler(async (req: Request, res: Response) => {
    const { filename } = req.params;
    const { expiresIn } = req.query;

    if (!filename) {
      throw new CustomError('Filename is required', 400);
    }

    const fileInfo = await fileService.getFileInfo(filename);
    if (!fileInfo.exists) {
      throw new CustomError('File not found', 404);
    }

    const secureUrl = fileService.generateSecureUrl(
      filename,
      expiresIn ? parseInt(expiresIn as string) : undefined
    );

    res.status(200).json({
      success: true,
      data: { url: secureUrl },
      message: 'Secure URL generated successfully',
    });
  });

  // Scan file for viruses
  scanFile = asyncHandler(async (req: Request, res: Response) => {
    const { filename } = req.params;

    if (!filename) {
      throw new CustomError('Filename is required', 400);
    }

    const fileInfo = await fileService.getFileInfo(filename);
    if (!fileInfo.exists) {
      throw new CustomError('File not found', 404);
    }

    // In a real implementation, you would scan the actual file
    const scanResult = await fileService.scanFile(filename);

    res.status(200).json({
      success: true,
      data: scanResult,
      message: scanResult.clean ? 'File is clean' : 'Threats detected',
    });
  });
}

export const fileController = new FileController();
