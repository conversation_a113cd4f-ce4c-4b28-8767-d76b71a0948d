import React, { useState, useCallback, useMemo } from "react";
import { Calendar, momentLocalizer, View, Views } from "react-big-calendar";
import moment from "moment";
import { Card, Button, Select, Space, Typography, Spin } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import styled from "styled-components";
import { media, spacing } from "@/styles/breakpoints";
import { useCalendarStore } from "@/store/calendarStore";
import { useCalendarEvents } from "@/hooks/useEvents";
import { Event } from "@/types";

import "react-big-calendar/lib/css/react-big-calendar.css";

const { Title } = Typography;
const { Option } = Select;

const localizer = momentLocalizer(moment);

const CalendarContainer = styled.div`
  .rbc-calendar {
    min-height: 500px;

    ${media.md} {
      min-height: 600px;
    }

    ${media.lg} {
      min-height: 700px;
    }
  }

  /* Mobile-first responsive styles */
  .rbc-toolbar {
    flex-direction: column;
    gap: ${spacing.mobile.md};
    margin-bottom: ${spacing.mobile.lg};

    ${media.md} {
      flex-direction: row;
      gap: ${spacing.desktop.md};
      margin-bottom: ${spacing.desktop.lg};
    }
  }

  .rbc-toolbar-label {
    font-size: 16px;
    font-weight: 600;
    text-align: center;

    ${media.md} {
      font-size: 18px;
    }
  }

  .rbc-btn-group {
    display: flex;
    gap: 4px;

    button {
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      background: white;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.rbc-active {
        background: #1890ff;
        color: white;
        border-color: #1890ff;
      }

      ${media.touch} {
        min-height: 44px;
        padding: 12px 16px;
      }
    }
  }

  /* Event styling */
  .rbc-event {
    background: #1890ff;
    border-radius: 4px;
    border: none;
    padding: 2px 6px;
    font-size: 12px;

    ${media.md} {
      font-size: 13px;
      padding: 4px 8px;
    }
  }

  /* Month view optimizations */
  .rbc-month-view {
    .rbc-date-cell {
      padding: 4px;

      ${media.md} {
        padding: 8px;
      }
    }

    .rbc-day-bg {
      ${media.maxSm} {
        min-height: 80px;
      }
    }
  }

  /* Week/Day view optimizations */
  .rbc-time-view {
    .rbc-time-gutter {
      width: 60px;

      ${media.md} {
        width: 80px;
      }
    }
  }

  /* Agenda view optimizations */
  .rbc-agenda-view {
    table {
      font-size: 14px;

      ${media.maxSm} {
        font-size: 12px;
      }
    }

    .rbc-agenda-date-cell {
      width: 80px;

      ${media.md} {
        width: 120px;
      }
    }
  }
`;

const CalendarHeader = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.mobile.md};
  margin-bottom: ${spacing.mobile.lg};

  ${media.md} {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: ${spacing.desktop.md};
    margin-bottom: ${spacing.desktop.lg};
  }
`;

const CalendarControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${spacing.mobile.sm};

  ${media.sm} {
    flex-direction: row;
    align-items: center;
    gap: ${spacing.mobile.md};
  }
`;

interface ResponsiveCalendarProps {
  onEventSelect?: (event: Event) => void;
  onSlotSelect?: (slotInfo: { start: Date; end: Date }) => void;
}

const ResponsiveCalendar: React.FC<ResponsiveCalendarProps> = ({
  onEventSelect,
  onSlotSelect,
}) => {
  const { view, setView, setDate, navigateCalendar } = useCalendarStore();
  const [currentDate, setCurrentDate] = useState(view.date);

  // Calculate date range for API call
  const { startDate, endDate } = useMemo(() => {
    const start = moment(currentDate)
      .startOf("month")
      .subtract(1, "week")
      .toDate();
    const end = moment(currentDate).endOf("month").add(1, "week").toDate();
    return { startDate: start, endDate: end };
  }, [currentDate]);

  const { data: events = [], isLoading } = useCalendarEvents(
    startDate,
    endDate
  );

  // Transform events for react-big-calendar
  const calendarEvents = useMemo(() => {
    return events.map((event: Event) => ({
      id: event._id,
      title: event.title,
      start: new Date(event.startDate),
      end: new Date(event.endDate),
      resource: event,
    }));
  }, [events]);

  const handleNavigate = useCallback(
    (date: Date) => {
      setCurrentDate(date);
      setDate(date);
    },
    [setDate]
  );

  const handleViewChange = useCallback(
    (newView: View) => {
      setView(newView as any);
    },
    [setView]
  );

  const handleSelectEvent = useCallback(
    (event: any) => {
      if (onEventSelect) {
        onEventSelect(event.resource);
      }
    },
    [onEventSelect]
  );

  const handleSelectSlot = useCallback(
    (slotInfo: any) => {
      if (onSlotSelect) {
        onSlotSelect(slotInfo);
      }
    },
    [onSlotSelect]
  );

  const goToToday = () => {
    const today = new Date();
    setCurrentDate(today);
    setDate(today);
  };

  const goToPrevious = () => {
    navigateCalendar("prev");
    const newDate = moment(currentDate)
      .subtract(
        1,
        view.view === "month" ? "month" : view.view === "week" ? "week" : "day"
      )
      .toDate();
    setCurrentDate(newDate);
  };

  const goToNext = () => {
    navigateCalendar("next");
    const newDate = moment(currentDate)
      .add(
        1,
        view.view === "month" ? "month" : view.view === "week" ? "week" : "day"
      )
      .toDate();
    setCurrentDate(newDate);
  };

  return (
    <Card>
      <CalendarHeader>
        <Title level={3} style={{ margin: 0 }}>
          Calendar
        </Title>

        <CalendarControls>
          <Space>
            <Button icon={<LeftOutlined />} onClick={goToPrevious} />
            <Button onClick={goToToday}>Today</Button>
            <Button icon={<RightOutlined />} onClick={goToNext} />
          </Space>

          <Select
            value={view.view}
            onChange={handleViewChange}
            style={{ width: 120 }}
          >
            <Option value="month">Month</Option>
            <Option value="week">Week</Option>
            <Option value="day">Day</Option>
            <Option value="agenda">Agenda</Option>
          </Select>
        </CalendarControls>
      </CalendarHeader>

      <Spin spinning={isLoading}>
        <CalendarContainer>
          <Calendar
            localizer={localizer}
            events={calendarEvents}
            startAccessor="start"
            endAccessor="end"
            style={{ height: "auto" }}
            view={view.view as View}
            views={[Views.MONTH, Views.WEEK, Views.DAY, Views.AGENDA]}
            date={currentDate}
            onNavigate={handleNavigate}
            onView={handleViewChange}
            onSelectEvent={handleSelectEvent}
            onSelectSlot={handleSelectSlot}
            selectable
            popup
            showMultiDayTimes
            step={30}
            timeslots={2}
            toolbar={false} // We're using custom toolbar
          />
        </CalendarContainer>
      </Spin>
    </Card>
  );
};

export default ResponsiveCalendar;
