import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button } from 'antd';
import { WifiOutlined, ReloadOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { media } from '@/styles/breakpoints';

const OfflineAlert = styled(Alert)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  border-radius: 0;
  text-align: center;
  
  ${media.md} {
    top: 64px; /* Account for header height on desktop */
  }
  
  ${media.maxMd} {
    top: 56px; /* Account for header height on mobile */
  }
`;

const OfflineIndicator: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showRetry, setShowRetry] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowRetry(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setTimeout(() => setShowRetry(true), 3000); // Show retry after 3 seconds
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = () => {
    if (navigator.onLine) {
      setIsOnline(true);
      setShowRetry(false);
      window.location.reload();
    }
  };

  if (isOnline) {
    return null;
  }

  return (
    <OfflineAlert
      message={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 8 }}>
          <WifiOutlined />
          <span>You're offline. Some features may not be available.</span>
          {showRetry && (
            <Button
              type="link"
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRetry}
              style={{ color: 'inherit', padding: 0, height: 'auto' }}
            >
              Retry
            </Button>
          )}
        </div>
      }
      type="warning"
      showIcon={false}
      closable={false}
    />
  );
};

export default OfflineIndicator;
