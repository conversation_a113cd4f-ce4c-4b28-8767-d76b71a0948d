import { Router } from 'express';
import { registrationController } from '../controllers/registrationController';
import { 
  authenticate, 
  canCreateEvents, 
  canEditEvents 
} from '../middleware/auth';
import { 
  validateObjectId, 
  sanitizeInput 
} from '../middleware/validation';

const router = Router();

// All registration routes require authentication
router.use(authenticate);

// Register for an event
router.post('/events/:id/register', 
  validateObjectId('id'),
  sanitizeInput,
  registrationController.registerForEvent
);

// Cancel registration
router.delete('/events/:id/register', 
  validateObjectId('id'),
  registrationController.cancelRegistration
);

// Check registration status for current user
router.get('/events/:id/registration-status', 
  validateObjectId('id'),
  registrationController.checkRegistrationStatus
);

// Get user's own registrations
router.get('/my-registrations', 
  registrationController.getUserRegistrations
);

// Get event registrations (admin/editor only)
router.get('/events/:id/registrations', 
  validateObjectId('id'),
  canEditEvents,
  registrationController.getEventRegistrations
);

// Get registration statistics (admin/editor only)
router.get('/stats', 
  canEditEvents,
  registrationController.getRegistrationStats
);

export default router;
