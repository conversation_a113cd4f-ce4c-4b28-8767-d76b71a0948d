import { apiClient } from '@/utils/apiClient';

export interface RegistrationData {
  eventId: string;
  userEmail?: string;
  userName?: string;
  notes?: string;
}

export interface Registration {
  _id: string;
  eventId: string;
  userId: string;
  userEmail: string;
  userName: string;
  registrationDate: Date;
  status: 'confirmed' | 'waitlist' | 'cancelled';
  notes?: string;
}

export interface RegistrationStats {
  total: number;
  confirmed: number;
  waitlist: number;
  cancelled: number;
}

class RegistrationService {
  private baseUrl = '/api/registrations';

  // Register for an event
  async registerForEvent(data: RegistrationData): Promise<Registration> {
    const response = await apiClient.post(`/api/events/${data.eventId}/register`, {
      userEmail: data.userEmail,
      userName: data.userName,
      notes: data.notes,
    });
    return response.data.data;
  }

  // Cancel registration
  async cancelRegistration(eventId: string, userId: string): Promise<Registration> {
    const response = await apiClient.delete(`/api/events/${eventId}/register`);
    return response.data.data;
  }

  // Get user's registrations
  async getUserRegistrations(userId: string): Promise<Registration[]> {
    const response = await apiClient.get(`${this.baseUrl}/my-registrations`);
    return response.data.data;
  }

  // Get event registrations (admin/editor only)
  async getEventRegistrations(eventId: string, status?: string): Promise<Registration[]> {
    const params = status ? { status } : {};
    const response = await apiClient.get(`/api/events/${eventId}/registrations`, { params });
    return response.data.data;
  }

  // Check registration status for user and event
  async checkRegistrationStatus(eventId: string, userId: string): Promise<{
    isRegistered: boolean;
    registration: Registration | null;
  }> {
    const response = await apiClient.get(`/api/events/${eventId}/registration-status`);
    return response.data.data;
  }

  // Get registration statistics
  async getRegistrationStats(eventId?: string): Promise<RegistrationStats> {
    const params = eventId ? { eventId } : {};
    const response = await apiClient.get(`${this.baseUrl}/stats`, { params });
    return response.data.data;
  }

  // Update registration status (admin/editor only)
  async updateRegistrationStatus(registrationId: string, status: string): Promise<Registration> {
    const response = await apiClient.patch(`${this.baseUrl}/${registrationId}/status`, {
      status,
    });
    return response.data.data;
  }

  // Delete registration (admin only)
  async deleteRegistration(registrationId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${registrationId}`);
  }

  // Bulk update registrations (admin/editor only)
  async bulkUpdateRegistrations(registrationIds: string[], status: string): Promise<void> {
    await apiClient.patch(`${this.baseUrl}/bulk-update`, {
      registrationIds,
      status,
    });
  }

  // Export registrations (admin/editor only)
  async exportRegistrations(eventId: string, format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const response = await apiClient.get(`/api/events/${eventId}/registrations/export`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  }

  // Send registration confirmation email
  async sendConfirmationEmail(registrationId: string): Promise<void> {
    await apiClient.post(`${this.baseUrl}/${registrationId}/send-confirmation`);
  }

  // Send bulk emails to registrants
  async sendBulkEmail(eventId: string, data: {
    subject: string;
    message: string;
    recipients: 'all' | 'confirmed' | 'waitlist';
  }): Promise<void> {
    await apiClient.post(`/api/events/${eventId}/registrations/send-email`, data);
  }

  // Get registration analytics
  async getRegistrationAnalytics(eventId: string): Promise<{
    registrationTrend: Array<{ date: string; count: number }>;
    statusDistribution: Record<string, number>;
    registrationsByDay: Record<string, number>;
    cancellationRate: number;
    averageRegistrationTime: number;
  }> {
    const response = await apiClient.get(`/api/events/${eventId}/registrations/analytics`);
    return response.data.data;
  }

  // Check-in attendee (for event day)
  async checkInAttendee(registrationId: string): Promise<Registration> {
    const response = await apiClient.patch(`${this.baseUrl}/${registrationId}/check-in`);
    return response.data.data;
  }

  // Get check-in statistics
  async getCheckInStats(eventId: string): Promise<{
    totalRegistered: number;
    checkedIn: number;
    checkInRate: number;
    noShows: number;
  }> {
    const response = await apiClient.get(`/api/events/${eventId}/registrations/check-in-stats`);
    return response.data.data;
  }

  // Generate QR code for registration
  async generateQRCode(registrationId: string): Promise<string> {
    const response = await apiClient.get(`${this.baseUrl}/${registrationId}/qr-code`);
    return response.data.data.qrCode;
  }

  // Validate QR code for check-in
  async validateQRCode(qrCode: string): Promise<{
    valid: boolean;
    registration?: Registration;
    message: string;
  }> {
    const response = await apiClient.post(`${this.baseUrl}/validate-qr`, { qrCode });
    return response.data.data;
  }

  // Get waitlist position
  async getWaitlistPosition(registrationId: string): Promise<{
    position: number;
    totalWaitlist: number;
    estimatedWaitTime?: string;
  }> {
    const response = await apiClient.get(`${this.baseUrl}/${registrationId}/waitlist-position`);
    return response.data.data;
  }

  // Promote from waitlist
  async promoteFromWaitlist(eventId: string, count: number = 1): Promise<Registration[]> {
    const response = await apiClient.post(`/api/events/${eventId}/registrations/promote-waitlist`, {
      count,
    });
    return response.data.data;
  }

  // Get registration reminders
  async getRegistrationReminders(eventId: string): Promise<Array<{
    type: 'registration_deadline' | 'event_reminder' | 'check_in';
    scheduledDate: Date;
    sent: boolean;
    recipientCount: number;
  }>> {
    const response = await apiClient.get(`/api/events/${eventId}/registrations/reminders`);
    return response.data.data;
  }

  // Schedule registration reminder
  async scheduleReminder(eventId: string, data: {
    type: 'registration_deadline' | 'event_reminder' | 'check_in';
    scheduledDate: Date;
    message: string;
    recipients: 'all' | 'confirmed' | 'waitlist';
  }): Promise<void> {
    await apiClient.post(`/api/events/${eventId}/registrations/schedule-reminder`, data);
  }
}

export const registrationService = new RegistrationService();
