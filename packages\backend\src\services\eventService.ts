import { Event, EventDocument } from "../models/Event";
import {
  CreateEventRequest,
  UpdateEventRequest,
  EventFilters,
  EventSearchParams,
  PaginatedEventsResponse,
  EventStatus,
  EventType,
  EventPriority,
  EventStats,
  Department,
} from "@gbf-calendar/shared";
import { CustomError } from "../middleware/errorHandler";
import { logger, logDbOperation } from "../utils/logger";
import Registration from "../models/Registration";

export class EventService {
  // Create new event
  async createEvent(
    eventData: CreateEventRequest,
    createdBy: string
  ): Promise<EventDocument> {
    try {
      const event = new Event({
        ...eventData,
        createdBy,
      });

      await event.save();
      await event.populate("createdBy", "firstName lastName email");

      logDbOperation("create", "events", {
        eventId: event._id,
        title: event.title,
      });
      return event;
    } catch (error) {
      logger.error("Create event service error", error);
      throw new CustomError("Failed to create event", 500);
    }
  }

  // Get event by ID
  async getEventById(eventId: string): Promise<EventDocument> {
    try {
      const event = await Event.findById(eventId).populate(
        "createdBy",
        "firstName lastName email"
      );

      if (!event) {
        throw new CustomError("Event not found", 404);
      }

      return event;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error("Get event by ID service error", error);
      throw new CustomError("Failed to get event", 500);
    }
  }

  // Update event
  async updateEvent(
    eventId: string,
    updateData: UpdateEventRequest,
    userId: string
  ): Promise<EventDocument> {
    try {
      const event = await Event.findById(eventId);

      if (!event) {
        throw new CustomError("Event not found", 404);
      }

      // Check if user can edit this event (creator or admin)
      if (event.createdBy.toString() !== userId) {
        // Additional role-based check would be done in the controller
        throw new CustomError("You can only edit events you created", 403);
      }

      const updatedEvent = await Event.findByIdAndUpdate(
        eventId,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate("createdBy", "firstName lastName email");

      if (!updatedEvent) {
        throw new CustomError("Event not found", 404);
      }

      logDbOperation("update", "events", {
        eventId,
        title: updatedEvent.title,
      });
      return updatedEvent;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error("Update event service error", error);
      throw new CustomError("Failed to update event", 500);
    }
  }

  // Delete event
  async deleteEvent(eventId: string, userId: string): Promise<void> {
    try {
      const event = await Event.findById(eventId);

      if (!event) {
        throw new CustomError("Event not found", 404);
      }

      // Check if user can delete this event (creator or admin)
      if (event.createdBy.toString() !== userId) {
        // Additional role-based check would be done in the controller
        throw new CustomError("You can only delete events you created", 403);
      }

      await Event.findByIdAndDelete(eventId);
      logDbOperation("delete", "events", { eventId, title: event.title });
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error("Delete event service error", error);
      throw new CustomError("Failed to delete event", 500);
    }
  }

  // Get events with filters and pagination
  async getEvents(
    searchParams: EventSearchParams
  ): Promise<PaginatedEventsResponse> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = "startDate",
        sortOrder = "asc",
        filters = {},
      } = searchParams;

      // Build query
      const query = this.buildEventQuery(filters);

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip
      const skip = (page - 1) * limit;

      // Execute query
      const [events, total] = await Promise.all([
        Event.find(query)
          .populate("createdBy", "firstName lastName email")
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Event.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        events,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      logger.error("Get events service error", error);
      throw new CustomError("Failed to get events", 500);
    }
  }

  // Get events for calendar view (by date range)
  async getEventsByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<EventDocument[]> {
    try {
      const events = await Event.find({
        $or: [
          // Events that start within the range
          { startDate: { $gte: startDate, $lte: endDate } },
          // Events that end within the range
          { endDate: { $gte: startDate, $lte: endDate } },
          // Events that span the entire range
          { startDate: { $lte: startDate }, endDate: { $gte: endDate } },
        ],
        status: { $ne: EventStatus.CANCELLED },
      })
        .populate("createdBy", "firstName lastName email")
        .sort({ startDate: 1 });

      return events;
    } catch (error) {
      logger.error("Get events by date range service error", error);
      throw new CustomError("Failed to get events by date range", 500);
    }
  }

  // Get upcoming events
  async getUpcomingEvents(limit: number = 10): Promise<EventDocument[]> {
    try {
      const now = new Date();
      const events = await Event.find({
        startDate: { $gte: now },
        status: EventStatus.PUBLISHED,
      })
        .populate("createdBy", "firstName lastName email")
        .sort({ startDate: 1 })
        .limit(limit);

      return events;
    } catch (error) {
      logger.error("Get upcoming events service error", error);
      throw new CustomError("Failed to get upcoming events", 500);
    }
  }

  // Search events by text
  async searchEvents(
    searchTerm: string,
    limit: number = 20
  ): Promise<EventDocument[]> {
    try {
      const events = await Event.find({
        $text: { $search: searchTerm },
        status: { $ne: EventStatus.CANCELLED },
      })
        .populate("createdBy", "firstName lastName email")
        .sort({ score: { $meta: "textScore" }, startDate: 1 })
        .limit(limit);

      return events;
    } catch (error) {
      logger.error("Search events service error", error);
      throw new CustomError("Failed to search events", 500);
    }
  }

  // Build MongoDB query from filters
  private buildEventQuery(filters: EventFilters): any {
    const query: any = {};

    // Date range filter
    if (filters.startDate || filters.endDate) {
      query.startDate = {};
      if (filters.startDate) {
        query.startDate.$gte = filters.startDate;
      }
      if (filters.endDate) {
        query.startDate.$lte = filters.endDate;
      }
    }

    // Event type filter
    if (filters.type && filters.type.length > 0) {
      query.type = { $in: filters.type };
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      query.status = { $in: filters.status };
    }

    // Priority filter
    if (filters.priority && filters.priority.length > 0) {
      query.priority = { $in: filters.priority };
    }

    // Department filter
    if (filters.department && filters.department.length > 0) {
      query.department = { $in: filters.department };
    }

    // Registration status filter
    if (filters.registrationStatus && filters.registrationStatus.length > 0) {
      query.registrationStatus = { $in: filters.registrationStatus };
    }

    // Registration required filter
    if (filters.registrationRequired !== undefined) {
      query.registrationRequired = filters.registrationRequired;
    }

    // Available spots filter
    if (filters.hasAvailableSpots) {
      query.$expr = {
        $or: [
          { $eq: ["$maxAttendees", null] }, // No limit set
          { $lt: ["$currentAttendees", "$maxAttendees"] }, // Has available spots
        ],
      };
    }

    // Tags filter
    if (filters.tags && filters.tags.length > 0) {
      query.tags = { $in: filters.tags };
    }

    // Location filter
    if (filters.location) {
      query.$or = [
        { location: { $regex: filters.location, $options: "i" } },
        { venue: { $regex: filters.location, $options: "i" } },
      ];
    }

    // Created by filter
    if (filters.createdBy) {
      query.createdBy = filters.createdBy;
    }

    // Text search filter
    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    return query;
  }

  // Get comprehensive event statistics
  async getEventStats(): Promise<EventStats> {
    try {
      const now = new Date();

      // Basic counts
      const [totalEvents, upcomingEvents, completedEvents, cancelledEvents] =
        await Promise.all([
          Event.countDocuments(),
          Event.countDocuments({
            startDate: { $gt: now },
            status: { $nin: [EventStatus.CANCELLED, EventStatus.DRAFT] },
          }),
          Event.countDocuments({ status: EventStatus.COMPLETED }),
          Event.countDocuments({ status: EventStatus.CANCELLED }),
        ]);

      // Events by type
      const eventsByTypeAgg = await Event.aggregate([
        { $group: { _id: "$type", count: { $sum: 1 } } },
      ]);
      const eventsByType = Object.values(EventType).reduce((acc, type) => {
        acc[type] = 0;
        return acc;
      }, {} as Record<EventType, number>);
      eventsByTypeAgg.forEach((item) => {
        eventsByType[item._id as EventType] = item.count;
      });

      // Events by department
      const eventsByDepartmentAgg = await Event.aggregate([
        { $group: { _id: "$department", count: { $sum: 1 } } },
      ]);
      const eventsByDepartment = Object.values(Department).reduce(
        (acc, dept) => {
          acc[dept] = 0;
          return acc;
        },
        {} as Record<Department, number>
      );
      eventsByDepartmentAgg.forEach((item) => {
        eventsByDepartment[item._id as Department] = item.count;
      });

      // Events by status
      const eventsByStatusAgg = await Event.aggregate([
        { $group: { _id: "$status", count: { $sum: 1 } } },
      ]);
      const eventsByStatus = Object.values(EventStatus).reduce(
        (acc, status) => {
          acc[status] = 0;
          return acc;
        },
        {} as Record<EventStatus, number>
      );
      eventsByStatusAgg.forEach((item) => {
        eventsByStatus[item._id as EventStatus] = item.count;
      });

      // Registration statistics
      const [totalRegistrations, popularEventsAgg] = await Promise.all([
        Registration.countDocuments({ status: { $ne: "cancelled" } }),
        Registration.aggregate([
          { $match: { status: "confirmed" } },
          { $group: { _id: "$eventId", count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 5 },
          {
            $lookup: {
              from: "events",
              localField: "_id",
              foreignField: "_id",
              as: "event",
            },
          },
          { $unwind: "$event" },
          {
            $project: {
              eventId: "$_id",
              title: "$event.title",
              registrations: "$count",
            },
          },
        ]),
      ]);

      // Calculate average attendance
      const eventsWithRegistrations = await Event.aggregate([
        {
          $lookup: {
            from: "registrations",
            localField: "_id",
            foreignField: "eventId",
            as: "registrations",
          },
        },
        {
          $project: {
            registrationCount: {
              $size: {
                $filter: {
                  input: "$registrations",
                  cond: { $ne: ["$$this.status", "cancelled"] },
                },
              },
            },
          },
        },
        {
          $group: {
            _id: null,
            averageAttendance: { $avg: "$registrationCount" },
          },
        },
      ]);

      const averageAttendance =
        eventsWithRegistrations[0]?.averageAttendance || 0;

      return {
        totalEvents,
        upcomingEvents,
        completedEvents,
        cancelledEvents,
        eventsByType,
        eventsByDepartment,
        eventsByStatus,
        registrationStats: {
          totalRegistrations,
          averageAttendance: Math.round(averageAttendance * 100) / 100,
          popularEvents: popularEventsAgg,
        },
      };
    } catch (error) {
      logger.error("Get event stats service error", error);
      throw new CustomError("Failed to get event statistics", 500);
    }
  }
}

export const eventService = new EventService();
