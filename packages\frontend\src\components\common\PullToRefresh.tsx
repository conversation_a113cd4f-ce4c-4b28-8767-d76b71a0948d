import React, { useState, useRef, useCallback } from 'react';
import { Spin } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { media } from '@/styles/breakpoints';

const PullContainer = styled.div`
  position: relative;
  overflow: hidden;
`;

const PullIndicator = styled.div<{ pulling: boolean; refreshing: boolean; pullDistance: number }>`
  position: absolute;
  top: ${props => props.pulling || props.refreshing ? '0' : '-60px'};
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  transition: ${props => props.pulling ? 'none' : 'top 0.3s ease'};
  z-index: 10;
  
  transform: ${props => props.pulling ? `translateY(${Math.min(props.pullDistance, 60)}px)` : 'none'};
  
  /* Only show on mobile */
  ${media.md} {
    display: none;
  }
`;

const PullContent = styled.div<{ pulling: boolean; refreshing: boolean; pullDistance: number }>`
  transition: ${props => props.pulling ? 'none' : 'transform 0.3s ease'};
  transform: ${props => {
    if (props.refreshing) return 'translateY(60px)';
    if (props.pulling) return `translateY(${Math.min(props.pullDistance, 60)}px)`;
    return 'translateY(0)';
  }};
`;

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  disabled?: boolean;
  threshold?: number;
}

const PullToRefresh: React.FC<PullToRefreshProps> = ({
  onRefresh,
  children,
  disabled = false,
  threshold = 60,
}) => {
  const [pulling, setPulling] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const startY = useRef(0);
  const currentY = useRef(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (disabled || refreshing) return;
    
    const container = containerRef.current;
    if (!container || container.scrollTop > 0) return;
    
    startY.current = e.touches[0].clientY;
  }, [disabled, refreshing]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (disabled || refreshing) return;
    
    const container = containerRef.current;
    if (!container || container.scrollTop > 0) return;
    
    currentY.current = e.touches[0].clientY;
    const distance = currentY.current - startY.current;
    
    if (distance > 0) {
      e.preventDefault();
      setPulling(true);
      setPullDistance(distance * 0.5); // Reduce pull distance for better feel
    }
  }, [disabled, refreshing]);

  const handleTouchEnd = useCallback(async () => {
    if (disabled || refreshing || !pulling) return;
    
    setPulling(false);
    
    if (pullDistance >= threshold) {
      setRefreshing(true);
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setRefreshing(false);
      }
    }
    
    setPullDistance(0);
  }, [disabled, refreshing, pulling, pullDistance, threshold, onRefresh]);

  const getIndicatorContent = () => {
    if (refreshing) {
      return <Spin size="small" />;
    }
    
    if (pullDistance >= threshold) {
      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ReloadOutlined />
          <span>Release to refresh</span>
        </div>
      );
    }
    
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <ReloadOutlined style={{ transform: `rotate(${pullDistance * 3}deg)` }} />
        <span>Pull to refresh</span>
      </div>
    );
  };

  return (
    <PullContainer
      ref={containerRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <PullIndicator
        pulling={pulling}
        refreshing={refreshing}
        pullDistance={pullDistance}
      >
        {getIndicatorContent()}
      </PullIndicator>
      
      <PullContent
        pulling={pulling}
        refreshing={refreshing}
        pullDistance={pullDistance}
      >
        {children}
      </PullContent>
    </PullContainer>
  );
};

export default PullToRefresh;
