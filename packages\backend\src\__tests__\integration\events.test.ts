import request from 'supertest';
import { app } from '../../app';
import { Event } from '../../models/Event';
import { Registration } from '../../models/Registration';
import { connectTestDB, closeTestDB, clearTestDB } from '../../utils/testDb';
import { generateTestToken, createTestUser } from '../../utils/testHelpers';

describe('Events API Integration', () => {
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    await connectTestDB();
    const testUser = await createTestUser();
    userId = testUser._id.toString();
    authToken = generateTestToken(testUser);
  });

  afterAll(async () => {
    await closeTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('Event CRUD Operations', () => {
    const validEventData = {
      title: 'Integration Test Event',
      description: 'A comprehensive integration test event',
      startDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
      location: 'Test Location',
      venue: 'Test Venue',
      spoc: 'Test SPOC',
      contactEmail: '<EMAIL>',
      contactPhone: '+1234567890',
      department: 'TECHNOLOGY',
      type: 'MEETING',
      priority: 'MEDIUM',
      status: 'DRAFT',
      registrationRequired: true,
      maxAttendees: 50,
      registrationDeadline: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
      waitlistEnabled: true,
      tags: ['test', 'integration'],
    };

    it('should create, read, update, and delete an event', async () => {
      // CREATE
      const createResponse = await request(app)
        .post('/api/events')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validEventData)
        .expect(201);

      expect(createResponse.body.success).toBe(true);
      expect(createResponse.body.data.title).toBe(validEventData.title);
      const eventId = createResponse.body.data._id;

      // READ
      const readResponse = await request(app)
        .get(`/api/events/${eventId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(readResponse.body.success).toBe(true);
      expect(readResponse.body.data._id).toBe(eventId);
      expect(readResponse.body.data.title).toBe(validEventData.title);

      // UPDATE
      const updateData = {
        title: 'Updated Event Title',
        description: 'Updated description',
        priority: 'HIGH',
      };

      const updateResponse = await request(app)
        .put(`/api/events/${eventId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(updateResponse.body.success).toBe(true);
      expect(updateResponse.body.data.title).toBe(updateData.title);
      expect(updateResponse.body.data.priority).toBe(updateData.priority);

      // DELETE
      await request(app)
        .delete(`/api/events/${eventId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify deletion
      await request(app)
        .get(`/api/events/${eventId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should handle event listing with filters and pagination', async () => {
      // Create multiple events
      const events = [];
      for (let i = 0; i < 15; i++) {
        const eventData = {
          ...validEventData,
          title: `Test Event ${i}`,
          department: i % 2 === 0 ? 'TECHNOLOGY' : 'MARKETING',
          priority: i % 3 === 0 ? 'HIGH' : 'MEDIUM',
          startDate: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(eventData);

        events.push(response.body.data);
      }

      // Test pagination
      const paginationResponse = await request(app)
        .get('/api/events?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(paginationResponse.body.success).toBe(true);
      expect(paginationResponse.body.data.events).toHaveLength(10);
      expect(paginationResponse.body.data.total).toBe(15);
      expect(paginationResponse.body.data.page).toBe(1);
      expect(paginationResponse.body.data.totalPages).toBe(2);

      // Test filtering by department
      const filterResponse = await request(app)
        .get('/api/events?department=TECHNOLOGY')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(filterResponse.body.success).toBe(true);
      expect(filterResponse.body.data.events.length).toBeGreaterThan(0);
      filterResponse.body.data.events.forEach((event: any) => {
        expect(event.department).toBe('TECHNOLOGY');
      });

      // Test sorting
      const sortResponse = await request(app)
        .get('/api/events?sortBy=startDate&sortOrder=desc')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(sortResponse.body.success).toBe(true);
      const sortedEvents = sortResponse.body.data.events;
      for (let i = 1; i < sortedEvents.length; i++) {
        expect(new Date(sortedEvents[i - 1].startDate).getTime())
          .toBeGreaterThanOrEqual(new Date(sortedEvents[i].startDate).getTime());
      }

      // Test search
      const searchResponse = await request(app)
        .get('/api/events?search=Test Event 5')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(searchResponse.body.success).toBe(true);
      expect(searchResponse.body.data.events).toHaveLength(1);
      expect(searchResponse.body.data.events[0].title).toBe('Test Event 5');
    });
  });

  describe('Event Registration Flow', () => {
    let eventId: string;

    beforeEach(async () => {
      const eventData = {
        title: 'Registration Test Event',
        description: 'Event for testing registration',
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
        location: 'Test Location',
        department: 'TECHNOLOGY',
        type: 'MEETING',
        priority: 'MEDIUM',
        status: 'PUBLISHED',
        registrationRequired: true,
        maxAttendees: 2,
        registrationDeadline: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
        waitlistEnabled: true,
        createdBy: userId,
      };

      const event = await Event.create(eventData);
      eventId = event._id.toString();
    });

    it('should handle complete registration workflow', async () => {
      // Register first user
      const registration1Data = {
        userEmail: '<EMAIL>',
        userName: 'User One',
        notes: 'First registration',
      };

      const reg1Response = await request(app)
        .post(`/api/events/${eventId}/register`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(registration1Data)
        .expect(201);

      expect(reg1Response.body.success).toBe(true);
      expect(reg1Response.body.data.status).toBe('confirmed');

      // Register second user
      const registration2Data = {
        userEmail: '<EMAIL>',
        userName: 'User Two',
        notes: 'Second registration',
      };

      const reg2Response = await request(app)
        .post(`/api/events/${eventId}/register`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(registration2Data)
        .expect(201);

      expect(reg2Response.body.success).toBe(true);
      expect(reg2Response.body.data.status).toBe('confirmed');

      // Try to register third user (should go to waitlist)
      const registration3Data = {
        userEmail: '<EMAIL>',
        userName: 'User Three',
        notes: 'Third registration - should be waitlisted',
      };

      const reg3Response = await request(app)
        .post(`/api/events/${eventId}/register`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(registration3Data)
        .expect(201);

      expect(reg3Response.body.success).toBe(true);
      expect(reg3Response.body.data.status).toBe('waitlist');

      // Check event registrations
      const registrationsResponse = await request(app)
        .get(`/api/events/${eventId}/registrations`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(registrationsResponse.body.success).toBe(true);
      expect(registrationsResponse.body.data).toHaveLength(3);

      const confirmedRegistrations = registrationsResponse.body.data.filter(
        (reg: any) => reg.status === 'confirmed'
      );
      const waitlistRegistrations = registrationsResponse.body.data.filter(
        (reg: any) => reg.status === 'waitlist'
      );

      expect(confirmedRegistrations).toHaveLength(2);
      expect(waitlistRegistrations).toHaveLength(1);

      // Cancel first registration
      await request(app)
        .delete(`/api/events/${eventId}/register`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Check that waitlisted user is automatically promoted
      const updatedRegistrationsResponse = await request(app)
        .get(`/api/events/${eventId}/registrations`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const updatedConfirmedRegistrations = updatedRegistrationsResponse.body.data.filter(
        (reg: any) => reg.status === 'confirmed'
      );

      expect(updatedConfirmedRegistrations).toHaveLength(2);
    });

    it('should prevent registration after deadline', async () => {
      // Update event to have past registration deadline
      await Event.findByIdAndUpdate(eventId, {
        registrationDeadline: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      });

      const registrationData = {
        userEmail: '<EMAIL>',
        userName: 'Late User',
      };

      const response = await request(app)
        .post(`/api/events/${eventId}/register`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(registrationData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('deadline');
    });

    it('should prevent duplicate registrations', async () => {
      const registrationData = {
        userEmail: '<EMAIL>',
        userName: 'Test User',
      };

      // First registration
      await request(app)
        .post(`/api/events/${eventId}/register`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(registrationData)
        .expect(201);

      // Attempt duplicate registration
      const duplicateResponse = await request(app)
        .post(`/api/events/${eventId}/register`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(registrationData)
        .expect(400);

      expect(duplicateResponse.body.success).toBe(false);
      expect(duplicateResponse.body.error).toContain('already registered');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid event ID format', async () => {
      const response = await request(app)
        .get('/api/events/invalid-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid id format');
    });

    it('should handle non-existent event', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      const response = await request(app)
        .get(`/api/events/${nonExistentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });

    it('should handle unauthorized access', async () => {
      const response = await request(app)
        .get('/api/events')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('token');
    });

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/events')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('{ invalid json }')
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle rate limiting', async () => {
      // This test would require configuring rate limiting for the test environment
      // and making multiple rapid requests to trigger the limit
      
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .get('/api/events')
            .set('Authorization', `Bearer ${authToken}`)
        );
      }

      const responses = await Promise.all(promises);
      
      // All requests should succeed in test environment
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });
  });

  describe('Data Consistency and Transactions', () => {
    it('should maintain data consistency during concurrent registrations', async () => {
      const eventData = {
        title: 'Concurrency Test Event',
        description: 'Event for testing concurrent registrations',
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
        location: 'Test Location',
        department: 'TECHNOLOGY',
        type: 'MEETING',
        priority: 'MEDIUM',
        status: 'PUBLISHED',
        registrationRequired: true,
        maxAttendees: 1, // Only one spot available
        waitlistEnabled: true,
        createdBy: userId,
      };

      const event = await Event.create(eventData);
      const eventId = event._id.toString();

      // Simulate concurrent registrations
      const registrationPromises = [];
      for (let i = 0; i < 5; i++) {
        registrationPromises.push(
          request(app)
            .post(`/api/events/${eventId}/register`)
            .set('Authorization', `Bearer ${authToken}`)
            .send({
              userEmail: `user${i}@example.com`,
              userName: `User ${i}`,
            })
        );
      }

      const responses = await Promise.all(registrationPromises);
      
      // Count successful registrations
      const successfulRegistrations = responses.filter(
        response => response.status === 201
      );

      // Should have exactly 5 registrations (1 confirmed, 4 waitlisted)
      expect(successfulRegistrations).toHaveLength(5);

      // Verify final state
      const finalRegistrations = await Registration.find({ eventId });
      expect(finalRegistrations).toHaveLength(5);

      const confirmedCount = finalRegistrations.filter(
        reg => reg.status === 'confirmed'
      ).length;
      const waitlistCount = finalRegistrations.filter(
        reg => reg.status === 'waitlist'
      ).length;

      expect(confirmedCount).toBe(1);
      expect(waitlistCount).toBe(4);
    });
  });
});
