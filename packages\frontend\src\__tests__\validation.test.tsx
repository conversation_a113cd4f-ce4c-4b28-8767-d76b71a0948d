import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act, renderHook } from '@testing-library/react';
import { z } from 'zod';
import { useFormValidation } from '../hooks/useFormValidation';
import { CreateEventSchema } from '@gbf-calendar/shared';
import EventForm from '../components/events/EventForm';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock dependencies
jest.mock('../hooks/useEvents', () => ({
  useCreateEvent: () => ({
    mutate: jest.fn(),
    isPending: false,
  }),
}));

jest.mock('../components/common/GlobalSearch', () => {
  return function MockGlobalSearch() {
    return <div data-testid="global-search">Global Search</div>;
  };
});

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Form Validation Hook', () => {
  const testSchema = z.object({
    name: z.string().min(1, 'Name is required').max(50, 'Name too long'),
    email: z.string().email('Invalid email'),
    age: z.number().min(18, 'Must be 18 or older').max(100, 'Age too high'),
  });

  it('should initialize with empty values and no errors', () => {
    const { result } = renderHook(() =>
      useFormValidation({}, { schema: testSchema })
    );

    expect(result.current.values).toEqual({});
    expect(result.current.errors).toEqual({});
    expect(result.current.isValid).toBe(true);
    expect(result.current.hasErrors).toBe(false);
  });

  it('should set and validate individual fields', async () => {
    const { result } = renderHook(() =>
      useFormValidation({}, { schema: testSchema, validateOnChange: true })
    );

    act(() => {
      result.current.setValue('name', 'John');
    });

    expect(result.current.values.name).toBe('John');
    expect(result.current.getFieldError('name')).toBeUndefined();
  });

  it('should show validation errors for invalid data', async () => {
    const { result } = renderHook(() =>
      useFormValidation({}, { schema: testSchema })
    );

    await act(async () => {
      const validationResult = await result.current.validate({
        name: '',
        email: 'invalid-email',
        age: 15,
      });

      expect(validationResult.isValid).toBe(false);
      expect(validationResult.errors).toHaveLength(3);
    });

    expect(result.current.hasFieldError('name')).toBe(true);
    expect(result.current.hasFieldError('email')).toBe(true);
    expect(result.current.hasFieldError('age')).toBe(true);
  });

  it('should validate individual fields', async () => {
    const { result } = renderHook(() =>
      useFormValidation({}, { schema: testSchema })
    );

    await act(async () => {
      const error = await result.current.validateField('email', 'invalid-email');
      expect(error).toEqual({
        field: 'email',
        message: 'Invalid email',
      });
    });
  });

  it('should clear errors when valid data is provided', async () => {
    const { result } = renderHook(() =>
      useFormValidation({}, { schema: testSchema })
    );

    // First, create some errors
    await act(async () => {
      await result.current.validate({
        name: '',
        email: 'invalid',
        age: 15,
      });
    });

    expect(result.current.hasErrors).toBe(true);

    // Then, provide valid data
    await act(async () => {
      await result.current.validate({
        name: 'John',
        email: '<EMAIL>',
        age: 25,
      });
    });

    expect(result.current.hasErrors).toBe(false);
    expect(result.current.isValid).toBe(true);
  });

  it('should reset form to initial state', () => {
    const initialValues = { name: 'Initial' };
    const { result } = renderHook(() =>
      useFormValidation(initialValues, { schema: testSchema })
    );

    act(() => {
      result.current.setValue('name', 'Changed');
      result.current.setError('email', 'Some error');
    });

    expect(result.current.values.name).toBe('Changed');
    expect(result.current.hasErrors).toBe(true);

    act(() => {
      result.current.reset();
    });

    expect(result.current.values).toEqual(initialValues);
    expect(result.current.hasErrors).toBe(false);
  });

  it('should handle debounced validation on change', async () => {
    jest.useFakeTimers();

    const { result } = renderHook(() =>
      useFormValidation({}, { 
        schema: testSchema, 
        validateOnChange: true,
        debounceMs: 300 
      })
    );

    act(() => {
      result.current.setValue('email', 'invalid');
    });

    // Error should not appear immediately
    expect(result.current.hasFieldError('email')).toBe(false);

    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.hasFieldError('email')).toBe(true);
    });

    jest.useRealTimers();
  });
});

describe('Event Form Validation', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderEventForm = (props = {}) => {
    return render(
      <EventForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        mode="create"
        {...props}
      />,
      { wrapper: createWrapper() }
    );
  };

  it('should render form fields', () => {
    renderEventForm();

    expect(screen.getByLabelText(/event title/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/start date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/end date/i)).toBeInTheDocument();
  });

  it('should show validation errors for empty required fields', async () => {
    const user = userEvent.setup();
    renderEventForm();

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/title is required/i)).toBeInTheDocument();
    });
  });

  it('should validate title length', async () => {
    const user = userEvent.setup();
    renderEventForm();

    const titleInput = screen.getByLabelText(/event title/i);
    const longTitle = 'a'.repeat(201); // Exceeds 200 character limit

    await user.type(titleInput, longTitle);

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/title must be at most 200 characters/i)).toBeInTheDocument();
    });
  });

  it('should validate date range', async () => {
    const user = userEvent.setup();
    renderEventForm();

    // Set end date before start date
    const startDateInput = screen.getByLabelText(/start date/i);
    const endDateInput = screen.getByLabelText(/end date/i);

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const today = new Date();

    await user.type(startDateInput, tomorrow.toISOString().split('T')[0]);
    await user.type(endDateInput, today.toISOString().split('T')[0]);

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/end date must be after start date/i)).toBeInTheDocument();
    });
  });

  it('should validate email format', async () => {
    const user = userEvent.setup();
    renderEventForm();

    const emailInput = screen.getByLabelText(/contact email/i);
    await user.type(emailInput, 'invalid-email');

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/invalid email/i)).toBeInTheDocument();
    });
  });

  it('should validate max attendees range', async () => {
    const user = userEvent.setup();
    renderEventForm();

    // Enable registration first
    const registrationSwitch = screen.getByRole('switch', { name: /registration required/i });
    await user.click(registrationSwitch);

    const maxAttendeesInput = screen.getByLabelText(/maximum attendees/i);
    await user.type(maxAttendeesInput, '10001'); // Exceeds limit

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/must be at most 10000/i)).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    renderEventForm();

    // Fill in required fields
    await user.type(screen.getByLabelText(/event title/i), 'Test Event');
    await user.type(screen.getByLabelText(/description/i), 'Test Description');

    // Set dates
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfter = new Date();
    dayAfter.setDate(dayAfter.getDate() + 2);

    const startDateInput = screen.getByLabelText(/start date/i);
    const endDateInput = screen.getByLabelText(/end date/i);

    await user.type(startDateInput, tomorrow.toISOString().slice(0, 16));
    await user.type(endDateInput, dayAfter.toISOString().slice(0, 16));

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Event',
          description: 'Test Description',
        })
      );
    });
  });

  it('should handle form cancellation', async () => {
    const user = userEvent.setup();
    renderEventForm();

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should validate registration deadline against start date', async () => {
    const user = userEvent.setup();
    renderEventForm();

    // Enable registration
    const registrationSwitch = screen.getByRole('switch', { name: /registration required/i });
    await user.click(registrationSwitch);

    // Set registration deadline after start date
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfter = new Date();
    dayAfter.setDate(dayAfter.getDate() + 2);

    const startDateInput = screen.getByLabelText(/start date/i);
    const registrationDeadlineInput = screen.getByLabelText(/registration deadline/i);

    await user.type(startDateInput, tomorrow.toISOString().slice(0, 16));
    await user.type(registrationDeadlineInput, dayAfter.toISOString().slice(0, 16));

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration deadline must be before/i)).toBeInTheDocument();
    });
  });
});

describe('Input Sanitization', () => {
  it('should sanitize XSS attempts in form inputs', async () => {
    const user = userEvent.setup();
    const { container } = render(
      <EventForm
        onSubmit={jest.fn()}
        onCancel={jest.fn()}
        mode="create"
      />,
      { wrapper: createWrapper() }
    );

    const titleInput = screen.getByLabelText(/event title/i);
    const maliciousInput = '<script>alert("xss")</script>';

    await user.type(titleInput, maliciousInput);

    // The input should not contain the script tag
    expect(titleInput).not.toHaveValue(maliciousInput);
  });

  it('should trim whitespace from inputs', async () => {
    const user = userEvent.setup();
    const mockOnSubmit = jest.fn();

    render(
      <EventForm
        onSubmit={mockOnSubmit}
        onCancel={jest.fn()}
        mode="create"
      />,
      { wrapper: createWrapper() }
    );

    const titleInput = screen.getByLabelText(/event title/i);
    await user.type(titleInput, '  Test Event  ');

    // Fill other required fields
    await user.type(screen.getByLabelText(/description/i), 'Test Description');

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfter = new Date();
    dayAfter.setDate(dayAfter.getDate() + 2);

    await user.type(screen.getByLabelText(/start date/i), tomorrow.toISOString().slice(0, 16));
    await user.type(screen.getByLabelText(/end date/i), dayAfter.toISOString().slice(0, 16));

    const submitButton = screen.getByRole('button', { name: /create event/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Event', // Should be trimmed
        })
      );
    });
  });
});
