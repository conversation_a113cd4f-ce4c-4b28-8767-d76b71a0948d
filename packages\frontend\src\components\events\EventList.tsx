import React, { useState, useEffect } from 'react';
import {
  List,
  Card,
  Tag,
  Space,
  Button,
  Avatar,
  Typography,
  Row,
  Col,
  Pagination,
  Empty,
  Spin,
  Badge,
  Tooltip,
  Dropdown,
  Menu,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  UserOutlined,
  TeamOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { Event, EventType, EventPriority, EventStatus, Department } from '@gbf-calendar/shared';

const { Text, Title } = Typography;

interface EventListProps {
  events: Event[];
  loading?: boolean;
  total?: number;
  current?: number;
  pageSize?: number;
  onPageChange?: (page: number, pageSize?: number) => void;
  onEventClick?: (event: Event) => void;
  onEditEvent?: (event: Event) => void;
  onDeleteEvent?: (event: Event) => void;
  onViewEvent?: (event: Event) => void;
  showActions?: boolean;
  userRole?: string;
}

const EventList: React.FC<EventListProps> = ({
  events,
  loading = false,
  total = 0,
  current = 1,
  pageSize = 10,
  onPageChange,
  onEventClick,
  onEditEvent,
  onDeleteEvent,
  onViewEvent,
  showActions = true,
  userRole = 'viewer',
}) => {
  const getEventStatusColor = (status: EventStatus) => {
    const colors = {
      [EventStatus.DRAFT]: 'default',
      [EventStatus.PUBLISHED]: 'blue',
      [EventStatus.CONFIRMED]: 'green',
      [EventStatus.PENDING]: 'orange',
      [EventStatus.CANCELLED]: 'red',
      [EventStatus.COMPLETED]: 'purple',
      [EventStatus.POSTPONED]: 'yellow',
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority: EventPriority) => {
    const colors = {
      [EventPriority.LOW]: 'green',
      [EventPriority.MEDIUM]: 'blue',
      [EventPriority.HIGH]: 'orange',
      [EventPriority.URGENT]: 'red',
      [EventPriority.CRITICAL]: 'red',
    };
    return colors[priority] || 'default';
  };

  const getDepartmentColor = (department: Department) => {
    const colors = {
      [Department.EXECUTIVE]: 'purple',
      [Department.FINANCE]: 'green',
      [Department.OPERATIONS]: 'blue',
      [Department.HUMAN_RESOURCES]: 'orange',
      [Department.MARKETING]: 'pink',
      [Department.SALES]: 'cyan',
      [Department.TECHNOLOGY]: 'geekblue',
      [Department.LEGAL]: 'red',
      [Department.COMPLIANCE]: 'yellow',
      [Department.STRATEGY]: 'magenta',
      [Department.RESEARCH]: 'lime',
      [Department.CUSTOMER_SERVICE]: 'volcano',
      [Department.ADMINISTRATION]: 'gold',
      [Department.OTHER]: 'default',
    };
    return colors[department] || 'default';
  };

  const formatEventType = (type: EventType) => {
    return type.replace(/_/g, ' ').toUpperCase();
  };

  const formatDepartment = (department: Department) => {
    return department.replace(/_/g, ' ').toUpperCase();
  };

  const isEventUpcoming = (startDate: Date) => {
    return moment(startDate).isAfter(moment());
  };

  const isEventOngoing = (startDate: Date, endDate: Date) => {
    const now = moment();
    return now.isBetween(moment(startDate), moment(endDate));
  };

  const getEventActions = (event: Event) => {
    const canEdit = userRole === 'admin' || userRole === 'editor';
    const canDelete = userRole === 'admin';

    const menuItems = [
      {
        key: 'view',
        icon: <EyeOutlined />,
        label: 'View Details',
        onClick: () => onViewEvent?.(event),
      },
      {
        key: 'share',
        icon: <ShareAltOutlined />,
        label: 'Share Event',
        onClick: () => {
          // Implement share functionality
          navigator.clipboard.writeText(`${window.location.origin}/events/${event._id}`);
        },
      },
    ];

    if (canEdit) {
      menuItems.push({
        key: 'edit',
        icon: <EditOutlined />,
        label: 'Edit Event',
        onClick: () => onEditEvent?.(event),
      });
    }

    if (canDelete) {
      menuItems.push({
        key: 'delete',
        icon: <DeleteOutlined />,
        label: 'Delete Event',
        onClick: () => onDeleteEvent?.(event),
        danger: true,
      });
    }

    return (
      <Dropdown
        menu={{
          items: menuItems,
        }}
        trigger={['click']}
      >
        <Button type="text" icon={<MoreOutlined />} />
      </Dropdown>
    );
  };

  const renderEventItem = (event: Event) => {
    const isUpcoming = isEventUpcoming(event.startDate);
    const isOngoing = isEventOngoing(event.startDate, event.endDate);

    return (
      <List.Item
        key={event._id}
        style={{
          cursor: onEventClick ? 'pointer' : 'default',
          padding: '16px 24px',
          borderRadius: '8px',
          marginBottom: '8px',
          border: '1px solid #f0f0f0',
          backgroundColor: isOngoing ? '#f6ffed' : '#fff',
        }}
        onClick={() => onEventClick?.(event)}
        actions={showActions ? [getEventActions(event)] : undefined}
      >
        <List.Item.Meta
          avatar={
            <Badge dot={isOngoing} color="green">
              <Avatar
                style={{
                  backgroundColor: getDepartmentColor(event.department),
                  color: '#fff',
                }}
                size="large"
              >
                {event.department.charAt(0).toUpperCase()}
              </Avatar>
            </Badge>
          }
          title={
            <Space direction="vertical" size={4}>
              <Space>
                <Title level={4} style={{ margin: 0 }}>
                  {event.title}
                </Title>
                {isUpcoming && (
                  <Tag color="blue" style={{ fontSize: '10px' }}>
                    UPCOMING
                  </Tag>
                )}
                {isOngoing && (
                  <Tag color="green" style={{ fontSize: '10px' }}>
                    LIVE
                  </Tag>
                )}
              </Space>
              <Space wrap>
                <Tag color={getEventStatusColor(event.status)}>
                  {event.status.toUpperCase()}
                </Tag>
                <Tag color={getPriorityColor(event.priority)}>
                  {event.priority.toUpperCase()}
                </Tag>
                <Tag color={getDepartmentColor(event.department)}>
                  {formatDepartment(event.department)}
                </Tag>
                <Tag>{formatEventType(event.type)}</Tag>
              </Space>
            </Space>
          }
          description={
            <Space direction="vertical" size={8} style={{ width: '100%' }}>
              {event.description && (
                <Text type="secondary" ellipsis={{ rows: 2 }}>
                  {event.description}
                </Text>
              )}
              
              <Row gutter={[16, 8]}>
                <Col>
                  <Space>
                    <CalendarOutlined />
                    <Text strong>
                      {moment(event.startDate).format('MMM DD, YYYY')}
                    </Text>
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <ClockCircleOutlined />
                    <Text>
                      {moment(event.startDate).format('HH:mm')} - {moment(event.endDate).format('HH:mm')}
                    </Text>
                  </Space>
                </Col>
                {event.location && (
                  <Col>
                    <Space>
                      <EnvironmentOutlined />
                      <Text>{event.location}</Text>
                    </Space>
                  </Col>
                )}
                {event.spoc && (
                  <Col>
                    <Space>
                      <UserOutlined />
                      <Text>SPOC: {event.spoc}</Text>
                    </Space>
                  </Col>
                )}
              </Row>

              {event.registrationRequired && (
                <Row gutter={[16, 8]}>
                  <Col>
                    <Space>
                      <TeamOutlined />
                      <Text>
                        {event.currentAttendees || 0}
                        {event.maxAttendees && ` / ${event.maxAttendees}`} attendees
                      </Text>
                      {event.maxAttendees && event.currentAttendees >= event.maxAttendees && (
                        <Tag color="red" size="small">FULL</Tag>
                      )}
                    </Space>
                  </Col>
                  {event.registrationDeadline && (
                    <Col>
                      <Text type="secondary">
                        Registration deadline: {moment(event.registrationDeadline).format('MMM DD, YYYY HH:mm')}
                      </Text>
                    </Col>
                  )}
                </Row>
              )}

              {event.tags && event.tags.length > 0 && (
                <Space wrap>
                  {event.tags.map(tag => (
                    <Tag key={tag} size="small">{tag}</Tag>
                  ))}
                </Space>
              )}
            </Space>
          }
        />
      </List.Item>
    );
  };

  if (loading) {
    return (
      <Card>
        <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '50px' }} />
      </Card>
    );
  }

  if (!events || events.length === 0) {
    return (
      <Card>
        <Empty
          description="No events found"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <Card>
      <List
        dataSource={events}
        renderItem={renderEventItem}
        pagination={false}
      />
      
      {total > pageSize && (
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Pagination
            current={current}
            total={total}
            pageSize={pageSize}
            onChange={onPageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) =>
              `${range[0]}-${range[1]} of ${total} events`
            }
          />
        </div>
      )}
    </Card>
  );
};

export default EventList;
