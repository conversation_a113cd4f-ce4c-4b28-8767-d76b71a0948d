import React, { useState } from 'react';
import {
  Upload,
  Button,
  List,
  Progress,
  Typography,
  Space,
  Tag,
  Tooltip,
  Modal,
  Image,
  message,
} from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileOutlined,
  FilePdfOutlined,
  <PERSON>WordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  <PERSON>ImageOutlined,
  FileZipOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Text } = Typography;

export interface FileAttachment {
  name: string;
  url: string;
  type: string;
  size?: number;
  uid?: string;
}

interface FileUploadProps {
  value?: FileAttachment[];
  onChange?: (files: FileAttachment[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  showPreview?: boolean;
  disabled?: boolean;
  listType?: 'text' | 'picture' | 'picture-card';
}

const FileUpload: React.FC<FileUploadProps> = ({
  value = [],
  onChange,
  maxFiles = 10,
  maxFileSize = 10, // 10MB default
  acceptedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/zip',
    'application/x-zip-compressed',
  ],
  showPreview = true,
  disabled = false,
  listType = 'text',
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>(
    value.map((file, index) => ({
      uid: file.uid || `${index}`,
      name: file.name,
      status: 'done',
      url: file.url,
      type: file.type,
      size: file.size,
    }))
  );
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<UploadFile | null>(null);

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
    if (fileType.includes('word')) return <FileWordOutlined style={{ color: '#1890ff' }} />;
    if (fileType.includes('excel') || fileType.includes('sheet')) return <FileExcelOutlined style={{ color: '#52c41a' }} />;
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return <FilePptOutlined style={{ color: '#fa8c16' }} />;
    if (fileType.includes('image')) return <FileImageOutlined style={{ color: '#722ed1' }} />;
    if (fileType.includes('zip')) return <FileZipOutlined style={{ color: '#13c2c2' }} />;
    return <FileOutlined />;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const isImageFile = (fileType: string) => {
    return fileType.startsWith('image/');
  };

  const beforeUpload = (file: File) => {
    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      message.error(`File type ${file.type} is not supported`);
      return false;
    }

    // Check file size
    const isLtMaxSize = file.size / 1024 / 1024 < maxFileSize;
    if (!isLtMaxSize) {
      message.error(`File must be smaller than ${maxFileSize}MB`);
      return false;
    }

    // Check max files
    if (fileList.length >= maxFiles) {
      message.error(`Maximum ${maxFiles} files allowed`);
      return false;
    }

    return false; // Prevent auto upload
  };

  const handleChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];

    // Limit the number of files
    newFileList = newFileList.slice(-maxFiles);

    setFileList(newFileList);

    // Convert to FileAttachment format
    const attachments: FileAttachment[] = newFileList
      .filter(file => file.status === 'done' || file.originFileObj)
      .map(file => ({
        name: file.name,
        url: file.url || (file.originFileObj ? URL.createObjectURL(file.originFileObj) : ''),
        type: file.type || file.originFileObj?.type || '',
        size: file.size || file.originFileObj?.size,
        uid: file.uid,
      }));

    onChange?.(attachments);
  };

  const handlePreview = (file: UploadFile) => {
    setPreviewFile(file);
    setPreviewVisible(true);
  };

  const handleDownload = (file: UploadFile) => {
    if (file.url) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      link.click();
    }
  };

  const handleRemove = (file: UploadFile) => {
    const newFileList = fileList.filter(item => item.uid !== file.uid);
    setFileList(newFileList);

    const attachments: FileAttachment[] = newFileList
      .filter(f => f.status === 'done' || f.originFileObj)
      .map(f => ({
        name: f.name,
        url: f.url || (f.originFileObj ? URL.createObjectURL(f.originFileObj) : ''),
        type: f.type || f.originFileObj?.type || '',
        size: f.size || f.originFileObj?.size,
        uid: f.uid,
      }));

    onChange?.(attachments);
  };

  const uploadProps: UploadProps = {
    fileList,
    beforeUpload,
    onChange: handleChange,
    onPreview: showPreview ? handlePreview : undefined,
    onRemove: handleRemove,
    multiple: maxFiles > 1,
    listType,
    disabled,
    accept: acceptedTypes.join(','),
  };

  const renderFileList = () => {
    if (listType === 'picture-card') {
      return null; // Upload component handles this
    }

    return (
      <List
        size="small"
        dataSource={fileList}
        renderItem={(file) => (
          <List.Item
            actions={[
              showPreview && (isImageFile(file.type || '') || file.url) && (
                <Tooltip title="Preview">
                  <Button
                    type="text"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(file)}
                  />
                </Tooltip>
              ),
              file.url && (
                <Tooltip title="Download">
                  <Button
                    type="text"
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownload(file)}
                  />
                </Tooltip>
              ),
              !disabled && (
                <Tooltip title="Remove">
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemove(file)}
                  />
                </Tooltip>
              ),
            ].filter(Boolean)}
          >
            <List.Item.Meta
              avatar={getFileIcon(file.type || '')}
              title={
                <Space>
                  <Text ellipsis style={{ maxWidth: 200 }}>
                    {file.name}
                  </Text>
                  {file.status === 'uploading' && (
                    <Tag color="blue">Uploading...</Tag>
                  )}
                  {file.status === 'error' && (
                    <Tag color="red">Error</Tag>
                  )}
                </Space>
              }
              description={
                <Space>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {formatFileSize(file.size)}
                  </Text>
                  {file.status === 'uploading' && file.percent && (
                    <Progress
                      percent={file.percent}
                      size="small"
                      style={{ width: 100 }}
                    />
                  )}
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  return (
    <div>
      <Upload {...uploadProps}>
        <Button icon={<UploadOutlined />} disabled={disabled || fileList.length >= maxFiles}>
          {listType === 'picture-card' ? 'Upload' : 'Select Files'}
        </Button>
      </Upload>

      <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
        <div>
          Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, Images, ZIP
        </div>
        <div>
          Maximum file size: {maxFileSize}MB • Maximum files: {maxFiles}
        </div>
        <div>
          Current: {fileList.length} / {maxFiles} files
        </div>
      </div>

      {listType !== 'picture-card' && renderFileList()}

      {/* Preview Modal */}
      <Modal
        open={previewVisible}
        title={previewFile?.name}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        {previewFile && (
          <div style={{ textAlign: 'center' }}>
            {isImageFile(previewFile.type || '') ? (
              <Image
                src={previewFile.url || (previewFile.originFileObj ? URL.createObjectURL(previewFile.originFileObj) : '')}
                alt={previewFile.name}
                style={{ maxWidth: '100%', maxHeight: '70vh' }}
              />
            ) : (
              <div style={{ padding: '40px' }}>
                <Space direction="vertical" align="center">
                  {getFileIcon(previewFile.type || '')}
                  <Text strong>{previewFile.name}</Text>
                  <Text type="secondary">{formatFileSize(previewFile.size)}</Text>
                  {previewFile.url && (
                    <Button
                      type="primary"
                      icon={<DownloadOutlined />}
                      onClick={() => handleDownload(previewFile)}
                    >
                      Download File
                    </Button>
                  )}
                </Space>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FileUpload;
