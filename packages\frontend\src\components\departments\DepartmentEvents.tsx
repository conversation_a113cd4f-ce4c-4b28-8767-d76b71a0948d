import React, { useState } from 'react';
import {
  Card,
  Typography,
  Space,
  Avatar,
  Button,
  Row,
  Col,
  Statistic,
  Timeline,
  Calendar,
  Badge,
  Select,
  DatePicker,
  Tag,
  List,
  Empty,
} from 'antd';
import {
  CalendarOutlined,
  TeamOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  UserOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { Department, Event, EventStatus, EventPriority } from '@gbf-calendar/shared';

const { Title, Text } = Typography;
const { Option } = Select;

interface DepartmentEventsProps {
  department: Department;
  events: Event[];
  loading?: boolean;
  onBack?: () => void;
  onCreateEvent?: () => void;
  onViewEvent?: (event: Event) => void;
  onEditEvent?: (event: Event) => void;
}

const DepartmentEvents: React.FC<DepartmentEventsProps> = ({
  department,
  events,
  loading = false,
  onBack,
  onCreateEvent,
  onViewEvent,
  onEditEvent,
}) => {
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'timeline'>('list');
  const [statusFilter, setStatusFilter] = useState<EventStatus | 'all'>('all');
  const [priorityFilter, setPriorityFilter] = useState<EventPriority | 'all'>('all');

  const getDepartmentColor = (dept: Department) => {
    const colors = {
      [Department.EXECUTIVE]: 'purple',
      [Department.FINANCE]: 'green',
      [Department.OPERATIONS]: 'blue',
      [Department.HUMAN_RESOURCES]: 'orange',
      [Department.MARKETING]: 'pink',
      [Department.SALES]: 'cyan',
      [Department.TECHNOLOGY]: 'geekblue',
      [Department.LEGAL]: 'red',
      [Department.COMPLIANCE]: 'yellow',
      [Department.STRATEGY]: 'magenta',
      [Department.RESEARCH]: 'lime',
      [Department.CUSTOMER_SERVICE]: 'volcano',
      [Department.ADMINISTRATION]: 'gold',
      [Department.OTHER]: 'default',
    };
    return colors[dept] || 'default';
  };

  const formatDepartmentName = (dept: Department) => {
    return dept.replace(/_/g, ' ').toUpperCase();
  };

  const getStatusColor = (status: EventStatus) => {
    const colors = {
      [EventStatus.DRAFT]: 'default',
      [EventStatus.PUBLISHED]: 'blue',
      [EventStatus.CONFIRMED]: 'green',
      [EventStatus.PENDING]: 'orange',
      [EventStatus.CANCELLED]: 'red',
      [EventStatus.COMPLETED]: 'purple',
      [EventStatus.POSTPONED]: 'yellow',
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority: EventPriority) => {
    const colors = {
      [EventPriority.LOW]: 'green',
      [EventPriority.MEDIUM]: 'blue',
      [EventPriority.HIGH]: 'orange',
      [EventPriority.URGENT]: 'red',
      [EventPriority.CRITICAL]: 'red',
    };
    return colors[priority] || 'default';
  };

  const filteredEvents = events.filter(event => {
    if (statusFilter !== 'all' && event.status !== statusFilter) return false;
    if (priorityFilter !== 'all' && event.priority !== priorityFilter) return false;
    return true;
  });

  const getEventStats = () => {
    const now = moment();
    const upcoming = filteredEvents.filter(e => moment(e.startDate).isAfter(now));
    const ongoing = filteredEvents.filter(e => 
      now.isBetween(moment(e.startDate), moment(e.endDate))
    );
    const completed = filteredEvents.filter(e => e.status === EventStatus.COMPLETED);
    const totalAttendees = filteredEvents.reduce((sum, e) => sum + (e.currentAttendees || 0), 0);

    return {
      total: filteredEvents.length,
      upcoming: upcoming.length,
      ongoing: ongoing.length,
      completed: completed.length,
      totalAttendees,
    };
  };

  const stats = getEventStats();

  const renderEventItem = (event: Event) => (
    <List.Item
      key={event._id}
      actions={[
        <Button type="text" size="small" onClick={() => onViewEvent?.(event)}>
          View
        </Button>,
        <Button type="text" size="small" onClick={() => onEditEvent?.(event)}>
          Edit
        </Button>,
      ]}
    >
      <List.Item.Meta
        title={
          <Space>
            <Text strong>{event.title}</Text>
            <Tag color={getStatusColor(event.status)}>{event.status.toUpperCase()}</Tag>
            <Tag color={getPriorityColor(event.priority)}>{event.priority.toUpperCase()}</Tag>
          </Space>
        }
        description={
          <Space direction="vertical" size={4}>
            <Space wrap>
              <Space size={4}>
                <CalendarOutlined />
                <Text>{moment(event.startDate).format('MMM DD, YYYY')}</Text>
              </Space>
              <Space size={4}>
                <ClockCircleOutlined />
                <Text>{moment(event.startDate).format('HH:mm')}</Text>
              </Space>
              {event.location && (
                <Space size={4}>
                  <EnvironmentOutlined />
                  <Text>{event.location}</Text>
                </Space>
              )}
              {event.spoc && (
                <Space size={4}>
                  <UserOutlined />
                  <Text>{event.spoc}</Text>
                </Space>
              )}
            </Space>
            {event.registrationRequired && (
              <Space>
                <TeamOutlined />
                <Text>
                  {event.currentAttendees || 0}
                  {event.maxAttendees && ` / ${event.maxAttendees}`} attendees
                </Text>
              </Space>
            )}
          </Space>
        }
      />
    </List.Item>
  );

  const renderCalendarView = () => (
    <Calendar
      dateCellRender={(date) => {
        const dayEvents = filteredEvents.filter(event =>
          moment(event.startDate).isSame(date, 'day')
        );
        
        return (
          <div>
            {dayEvents.map(event => (
              <Badge
                key={event._id}
                status={getStatusColor(event.status) as any}
                text={
                  <Text
                    ellipsis
                    style={{ fontSize: '11px', cursor: 'pointer' }}
                    onClick={() => onViewEvent?.(event)}
                  >
                    {event.title}
                  </Text>
                }
              />
            ))}
          </div>
        );
      }}
    />
  );

  const renderTimelineView = () => (
    <Timeline mode="left">
      {filteredEvents
        .sort((a, b) => moment(a.startDate).unix() - moment(b.startDate).unix())
        .map(event => (
          <Timeline.Item
            key={event._id}
            color={getStatusColor(event.status)}
            label={moment(event.startDate).format('MMM DD')}
          >
            <Card size="small" style={{ cursor: 'pointer' }} onClick={() => onViewEvent?.(event)}>
              <Space direction="vertical" size={4}>
                <Space>
                  <Text strong>{event.title}</Text>
                  <Tag color={getPriorityColor(event.priority)} size="small">
                    {event.priority.toUpperCase()}
                  </Tag>
                </Space>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {moment(event.startDate).format('HH:mm')} - {moment(event.endDate).format('HH:mm')}
                </Text>
                {event.location && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    📍 {event.location}
                  </Text>
                )}
              </Space>
            </Card>
          </Timeline.Item>
        ))}
    </Timeline>
  );

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      {/* Header */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button icon={<ArrowLeftOutlined />} onClick={onBack}>
                Back
              </Button>
              <Avatar
                style={{
                  backgroundColor: getDepartmentColor(department),
                  color: '#fff',
                }}
                size="large"
              >
                {department.charAt(0).toUpperCase()}
              </Avatar>
              <div>
                <Title level={3} style={{ margin: 0 }}>
                  {formatDepartmentName(department)}
                </Title>
                <Text type="secondary">Department Events</Text>
              </div>
            </Space>
          </Col>
          <Col>
            <Button type="primary" icon={<PlusOutlined />} onClick={onCreateEvent}>
              Create Event
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Statistics */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Events"
              value={stats.total}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Upcoming"
              value={stats.upcoming}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Ongoing"
              value={stats.ongoing}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Attendees"
              value={stats.totalAttendees}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Controls */}
      <Card size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Select
                value={viewMode}
                onChange={setViewMode}
                style={{ width: 120 }}
              >
                <Option value="list">List View</Option>
                <Option value="calendar">Calendar</Option>
                <Option value="timeline">Timeline</Option>
              </Select>
              <Select
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
                placeholder="Status"
              >
                <Option value="all">All Status</Option>
                {Object.values(EventStatus).map(status => (
                  <Option key={status} value={status}>
                    {status.toUpperCase()}
                  </Option>
                ))}
              </Select>
              <Select
                value={priorityFilter}
                onChange={setPriorityFilter}
                style={{ width: 120 }}
                placeholder="Priority"
              >
                <Option value="all">All Priority</Option>
                {Object.values(EventPriority).map(priority => (
                  <Option key={priority} value={priority}>
                    {priority.toUpperCase()}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col>
            <Text type="secondary">
              {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''} found
            </Text>
          </Col>
        </Row>
      </Card>

      {/* Content */}
      <Card>
        {filteredEvents.length === 0 ? (
          <Empty
            description="No events found for this department"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <>
            {viewMode === 'list' && (
              <List
                dataSource={filteredEvents}
                renderItem={renderEventItem}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                }}
              />
            )}
            {viewMode === 'calendar' && renderCalendarView()}
            {viewMode === 'timeline' && renderTimelineView()}
          </>
        )}
      </Card>
    </Space>
  );
};

export default DepartmentEvents;
