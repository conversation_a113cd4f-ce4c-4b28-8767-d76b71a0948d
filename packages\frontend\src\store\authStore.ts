import { create } from "zustand";
import { persist } from "zustand/middleware";
import { AuthState, UserProfile } from "@/types";

interface AuthActions {
  setAuth: (user: UserProfile, token: string, refreshToken: string) => void;
  updateUser: (user: Partial<UserProfile>) => void;
  clearAuth: () => void;
  setLoading: (loading: boolean) => void;
}

type AuthStore = AuthState & AuthActions;

const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setAuth: (user, token, refreshToken) => {
        set({
          user,
          token,
          refreshToken,
          isAuthenticated: true,
          isLoading: false,
        });
      },

      updateUser: (userData) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },

      clearAuth: () => {
        set(initialState);
        // Clear tokens from localStorage
        localStorage.removeItem("token");
        localStorage.removeItem("refreshToken");
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Selectors
export const useAuth = () =>
  useAuthStore((state) => ({
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
  }));

export const useAuthActions = () =>
  useAuthStore((state) => ({
    setAuth: state.setAuth,
    updateUser: state.updateUser,
    clearAuth: state.clearAuth,
    setLoading: state.setLoading,
  }));

// Helper functions
export const getAuthToken = (): string | null => {
  return useAuthStore.getState().token;
};

export const getRefreshToken = (): string | null => {
  return useAuthStore.getState().refreshToken;
};

export const isUserAuthenticated = (): boolean => {
  return useAuthStore.getState().isAuthenticated;
};

export const getCurrentUser = (): UserProfile | null => {
  return useAuthStore.getState().user;
};

export const hasPermission = (
  permission: "create" | "edit" | "delete" | "manage"
): boolean => {
  const user = getCurrentUser();
  if (!user) return false;

  switch (permission) {
    case "create":
    case "edit":
      return ["admin", "editor"].includes(user.role);
    case "delete":
    case "manage":
      return user.role === "admin";
    default:
      return false;
  }
};
