import { Request, Response } from "express";
import { registrationService } from "../services/registrationService";
import { asyncHand<PERSON> } from "../middleware/asyncHandler";
import { CreateRegistrationRequest } from "@gbf-calendar/shared";

export class RegistrationController {
  // Register for an event
  registerForEvent = asyncHandler(async (req: Request, res: Response) => {
    const { eventId } = req.params;
    const userId = req.user!._id;
    const { userEmail, userName, notes } = req.body;

    const registrationData: CreateRegistrationRequest = {
      eventId,
      userId,
      userEmail: userEmail || req.user!.email,
      userName: userName || `${req.user!.firstName} ${req.user!.lastName}`,
      notes,
    };

    const registration = await registrationService.registerForEvent(registrationData);

    res.status(201).json({
      success: true,
      data: registration,
      message: registration.status === "confirmed" 
        ? "Successfully registered for event"
        : "Added to waitlist for event",
    });
  });

  // Cancel registration
  cancelRegistration = asyncHandler(async (req: Request, res: Response) => {
    const { eventId } = req.params;
    const userId = req.user!._id;

    const registration = await registrationService.cancelRegistration(eventId, userId);

    res.status(200).json({
      success: true,
      data: registration,
      message: "Registration cancelled successfully",
    });
  });

  // Get event registrations (admin/editor only)
  getEventRegistrations = asyncHandler(async (req: Request, res: Response) => {
    const { eventId } = req.params;
    const { status } = req.query;

    const registrations = await registrationService.getEventRegistrations(
      eventId,
      status as string
    );

    res.status(200).json({
      success: true,
      data: registrations,
      message: "Event registrations retrieved successfully",
    });
  });

  // Get user's registrations
  getUserRegistrations = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!._id;

    const registrations = await registrationService.getUserRegistrations(userId);

    res.status(200).json({
      success: true,
      data: registrations,
      message: "User registrations retrieved successfully",
    });
  });

  // Get registration statistics
  getRegistrationStats = asyncHandler(async (req: Request, res: Response) => {
    const { eventId } = req.query;

    const stats = await registrationService.getRegistrationStats(eventId as string);

    res.status(200).json({
      success: true,
      data: stats,
      message: "Registration statistics retrieved successfully",
    });
  });

  // Check registration status for user and event
  checkRegistrationStatus = asyncHandler(async (req: Request, res: Response) => {
    const { eventId } = req.params;
    const userId = req.user!._id;

    const registration = await registrationService.getUserRegistrations(userId);
    const eventRegistration = registration.find(
      (reg) => reg.eventId.toString() === eventId && reg.status !== "cancelled"
    );

    res.status(200).json({
      success: true,
      data: {
        isRegistered: !!eventRegistration,
        registration: eventRegistration || null,
      },
      message: "Registration status retrieved successfully",
    });
  });
}

export const registrationController = new RegistrationController();
