import React, { useState } from 'react';
import {
  Modal,
  Descriptions,
  Tag,
  Space,
  Button,
  Typography,
  Divider,
  Row,
  Col,
  Card,
  Avatar,
  List,
  Progress,
  Alert,
  Tooltip,
  Badge,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  UserOutlined,
  TeamOutlined,
  PhoneOutlined,
  MailOutlined,
  LinkOutlined,
  FileOutlined,
  EditOutlined,
  DeleteOutlined,
  ShareAltOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import {
  Event,
  EventType,
  EventPriority,
  EventStatus,
  Department,
  RegistrationStatus,
} from '@gbf-calendar/shared';

const { Title, Text, Paragraph } = Typography;

interface EventDetailModalProps {
  event: Event | null;
  visible: boolean;
  onClose: () => void;
  onEdit?: (event: Event) => void;
  onDelete?: (event: Event) => void;
  onRegister?: (event: Event) => void;
  onCancelRegistration?: (event: Event) => void;
  userRole?: string;
  isRegistered?: boolean;
  registrationStatus?: 'confirmed' | 'waitlist' | 'cancelled';
  loading?: boolean;
}

const EventDetailModal: React.FC<EventDetailModalProps> = ({
  event,
  visible,
  onClose,
  onEdit,
  onDelete,
  onRegister,
  onCancelRegistration,
  userRole = 'viewer',
  isRegistered = false,
  registrationStatus,
  loading = false,
}) => {
  if (!event) return null;

  const canEdit = userRole === 'admin' || userRole === 'editor';
  const canDelete = userRole === 'admin';
  const canRegister = event.registrationRequired && !isRegistered;
  const canCancelRegistration = event.registrationRequired && isRegistered;

  const getStatusColor = (status: EventStatus) => {
    const colors = {
      [EventStatus.DRAFT]: 'default',
      [EventStatus.PUBLISHED]: 'blue',
      [EventStatus.CONFIRMED]: 'green',
      [EventStatus.PENDING]: 'orange',
      [EventStatus.CANCELLED]: 'red',
      [EventStatus.COMPLETED]: 'purple',
      [EventStatus.POSTPONED]: 'yellow',
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority: EventPriority) => {
    const colors = {
      [EventPriority.LOW]: 'green',
      [EventPriority.MEDIUM]: 'blue',
      [EventPriority.HIGH]: 'orange',
      [EventPriority.URGENT]: 'red',
      [EventPriority.CRITICAL]: 'red',
    };
    return colors[priority] || 'default';
  };

  const getDepartmentColor = (department: Department) => {
    const colors = {
      [Department.EXECUTIVE]: 'purple',
      [Department.FINANCE]: 'green',
      [Department.OPERATIONS]: 'blue',
      [Department.HUMAN_RESOURCES]: 'orange',
      [Department.MARKETING]: 'pink',
      [Department.SALES]: 'cyan',
      [Department.TECHNOLOGY]: 'geekblue',
      [Department.LEGAL]: 'red',
      [Department.COMPLIANCE]: 'yellow',
      [Department.STRATEGY]: 'magenta',
      [Department.RESEARCH]: 'lime',
      [Department.CUSTOMER_SERVICE]: 'volcano',
      [Department.ADMINISTRATION]: 'gold',
      [Department.OTHER]: 'default',
    };
    return colors[department] || 'default';
  };

  const formatEnumValue = (value: string) => {
    return value.replace(/_/g, ' ').toUpperCase();
  };

  const isEventUpcoming = () => {
    return moment(event.startDate).isAfter(moment());
  };

  const isEventOngoing = () => {
    const now = moment();
    return now.isBetween(moment(event.startDate), moment(event.endDate));
  };

  const getRegistrationProgress = () => {
    if (!event.maxAttendees) return 0;
    return Math.round((event.currentAttendees / event.maxAttendees) * 100);
  };

  const getRegistrationStatusText = () => {
    if (!isRegistered) return null;
    
    const statusTexts = {
      confirmed: { text: 'You are registered for this event', color: 'success' },
      waitlist: { text: 'You are on the waitlist for this event', color: 'warning' },
      cancelled: { text: 'Your registration was cancelled', color: 'error' },
    };
    
    return statusTexts[registrationStatus || 'confirmed'];
  };

  const handleShare = () => {
    const url = `${window.location.origin}/events/${event._id}`;
    navigator.clipboard.writeText(url);
    // You could add a notification here
  };

  const modalActions = [
    <Button key="close" onClick={onClose}>
      Close
    </Button>,
  ];

  if (canRegister) {
    modalActions.unshift(
      <Button
        key="register"
        type="primary"
        icon={<UserAddOutlined />}
        onClick={() => onRegister?.(event)}
        loading={loading}
        disabled={event.registrationStatus === RegistrationStatus.CLOSED || 
                 event.registrationStatus === RegistrationStatus.FULL}
      >
        {event.registrationStatus === RegistrationStatus.WAITLIST ? 'Join Waitlist' : 'Register'}
      </Button>
    );
  }

  if (canCancelRegistration) {
    modalActions.unshift(
      <Button
        key="cancel-registration"
        danger
        icon={<UserDeleteOutlined />}
        onClick={() => onCancelRegistration?.(event)}
        loading={loading}
      >
        Cancel Registration
      </Button>
    );
  }

  modalActions.unshift(
    <Button key="share" icon={<ShareAltOutlined />} onClick={handleShare}>
      Share
    </Button>
  );

  if (canEdit) {
    modalActions.unshift(
      <Button key="edit" icon={<EditOutlined />} onClick={() => onEdit?.(event)}>
        Edit
      </Button>
    );
  }

  if (canDelete) {
    modalActions.unshift(
      <Button key="delete" danger icon={<DeleteOutlined />} onClick={() => onDelete?.(event)}>
        Delete
      </Button>
    );
  }

  return (
    <Modal
      title={
        <Space>
          <Avatar
            style={{
              backgroundColor: getDepartmentColor(event.department),
              color: '#fff',
            }}
          >
            {event.department.charAt(0).toUpperCase()}
          </Avatar>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              {event.title}
            </Title>
            <Space>
              {isEventOngoing() && <Badge status="processing" text="Live Event" />}
              {isEventUpcoming() && <Badge status="default" text="Upcoming" />}
            </Space>
          </div>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={modalActions}
      width={800}
      style={{ top: 20 }}
    >
      {/* Registration Status Alert */}
      {isRegistered && getRegistrationStatusText() && (
        <Alert
          message={getRegistrationStatusText()?.text}
          type={getRegistrationStatusText()?.color as any}
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* Event Status Tags */}
      <Space wrap style={{ marginBottom: 16 }}>
        <Tag color={getStatusColor(event.status)} style={{ fontSize: '14px', padding: '4px 8px' }}>
          {event.status.toUpperCase()}
        </Tag>
        <Tag color={getPriorityColor(event.priority)} style={{ fontSize: '14px', padding: '4px 8px' }}>
          {event.priority.toUpperCase()} PRIORITY
        </Tag>
        <Tag color={getDepartmentColor(event.department)} style={{ fontSize: '14px', padding: '4px 8px' }}>
          {formatEnumValue(event.department)}
        </Tag>
        <Tag style={{ fontSize: '14px', padding: '4px 8px' }}>
          {formatEnumValue(event.type)}
        </Tag>
      </Space>

      {/* Basic Information */}
      <Descriptions column={1} bordered size="small" style={{ marginBottom: 16 }}>
        <Descriptions.Item label={<><CalendarOutlined /> Date</>}>
          <Space direction="vertical" size={0}>
            <Text strong>{moment(event.startDate).format('dddd, MMMM DD, YYYY')}</Text>
            <Text type="secondary">
              {moment(event.startDate).format('HH:mm')} - {moment(event.endDate).format('HH:mm')}
              {' '}({moment.duration(moment(event.endDate).diff(moment(event.startDate))).humanize()})
            </Text>
          </Space>
        </Descriptions.Item>

        {event.location && (
          <Descriptions.Item label={<><EnvironmentOutlined /> Location</>}>
            {event.location}
            {event.venue && <Text type="secondary"> • {event.venue}</Text>}
          </Descriptions.Item>
        )}

        {event.spoc && (
          <Descriptions.Item label={<><UserOutlined /> SPOC</>}>
            <Space>
              <Text>{event.spoc}</Text>
              {event.contactEmail && (
                <Button
                  type="link"
                  size="small"
                  icon={<MailOutlined />}
                  href={`mailto:${event.contactEmail}`}
                >
                  {event.contactEmail}
                </Button>
              )}
              {event.contactPhone && (
                <Button
                  type="link"
                  size="small"
                  icon={<PhoneOutlined />}
                  href={`tel:${event.contactPhone}`}
                >
                  {event.contactPhone}
                </Button>
              )}
            </Space>
          </Descriptions.Item>
        )}
      </Descriptions>

      {/* Description */}
      {event.description && (
        <Card type="inner" title="Description" size="small" style={{ marginBottom: 16 }}>
          <Paragraph>{event.description}</Paragraph>
        </Card>
      )}

      {/* Agenda */}
      {event.agenda && (
        <Card type="inner" title="Agenda" size="small" style={{ marginBottom: 16 }}>
          <Paragraph style={{ whiteSpace: 'pre-wrap' }}>{event.agenda}</Paragraph>
        </Card>
      )}

      {/* Registration Information */}
      {event.registrationRequired && (
        <Card type="inner" title="Registration" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>Capacity: </Text>
                  <Text>
                    {event.currentAttendees || 0}
                    {event.maxAttendees && ` / ${event.maxAttendees}`} attendees
                  </Text>
                </div>
                {event.maxAttendees && (
                  <Progress
                    percent={getRegistrationProgress()}
                    status={getRegistrationProgress() >= 100 ? 'exception' : 'active'}
                    size="small"
                  />
                )}
              </Space>
            </Col>
            <Col span={12}>
              <Space direction="vertical">
                <div>
                  <Text strong>Status: </Text>
                  <Tag color={event.registrationStatus === RegistrationStatus.OPEN ? 'green' : 'red'}>
                    {formatEnumValue(event.registrationStatus)}
                  </Tag>
                </div>
                {event.registrationDeadline && (
                  <div>
                    <Text strong>Deadline: </Text>
                    <Text>{moment(event.registrationDeadline).format('MMM DD, YYYY HH:mm')}</Text>
                  </div>
                )}
                {event.registrationLink && (
                  <Button
                    type="link"
                    size="small"
                    icon={<LinkOutlined />}
                    href={event.registrationLink}
                    target="_blank"
                  >
                    External Registration
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* Tags */}
      {event.tags && event.tags.length > 0 && (
        <Card type="inner" title="Tags" size="small" style={{ marginBottom: 16 }}>
          <Space wrap>
            {event.tags.map(tag => (
              <Tag key={tag}>{tag}</Tag>
            ))}
          </Space>
        </Card>
      )}

      {/* Attachments */}
      {event.attachments && event.attachments.length > 0 && (
        <Card type="inner" title="Attachments" size="small">
          <List
            size="small"
            dataSource={event.attachments}
            renderItem={(attachment) => (
              <List.Item
                actions={[
                  <Button
                    type="link"
                    size="small"
                    icon={<FileOutlined />}
                    href={attachment.url}
                    target="_blank"
                  >
                    Download
                  </Button>
                ]}
              >
                <List.Item.Meta
                  title={attachment.name}
                  description={`${attachment.type}${attachment.size ? ` • ${(attachment.size / 1024 / 1024).toFixed(2)} MB` : ''}`}
                />
              </List.Item>
            )}
          />
        </Card>
      )}
    </Modal>
  );
};

export default EventDetailModal;
