import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Input,
  Select,
  Card,
  Statistic,
  Row,
  Col,
  Popconfirm,
  message,
  Tooltip,
  Badge,
  Export,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  SearchOutlined,
  DownloadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { Event } from '@gbf-calendar/shared';

const { Title, Text } = Typography;
const { Option } = Select;

interface Registration {
  _id: string;
  eventId: string;
  userId: string;
  userEmail: string;
  userName: string;
  registrationDate: Date;
  status: 'confirmed' | 'waitlist' | 'cancelled';
  notes?: string;
}

interface RegistrationStats {
  total: number;
  confirmed: number;
  waitlist: number;
  cancelled: number;
}

interface RegistrationManagementProps {
  event: Event | null;
  visible: boolean;
  onClose: () => void;
  registrations?: Registration[];
  stats?: RegistrationStats;
  loading?: boolean;
  onStatusChange?: (registrationId: string, newStatus: string) => void;
  onDeleteRegistration?: (registrationId: string) => void;
  onExportRegistrations?: () => void;
}

const RegistrationManagement: React.FC<RegistrationManagementProps> = ({
  event,
  visible,
  onClose,
  registrations = [],
  stats = { total: 0, confirmed: 0, waitlist: 0, cancelled: 0 },
  loading = false,
  onStatusChange,
  onDeleteRegistration,
  onExportRegistrations,
}) => {
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [filteredRegistrations, setFilteredRegistrations] = useState<Registration[]>([]);

  useEffect(() => {
    let filtered = registrations;

    // Filter by search text
    if (searchText) {
      filtered = filtered.filter(reg =>
        reg.userName.toLowerCase().includes(searchText.toLowerCase()) ||
        reg.userEmail.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(reg => reg.status === statusFilter);
    }

    setFilteredRegistrations(filtered);
  }, [registrations, searchText, statusFilter]);

  const getStatusColor = (status: string) => {
    const colors = {
      confirmed: 'green',
      waitlist: 'orange',
      cancelled: 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      confirmed: <CheckCircleOutlined />,
      waitlist: <ClockCircleOutlined />,
      cancelled: <DeleteOutlined />,
    };
    return icons[status as keyof typeof icons] || null;
  };

  const handleStatusChange = (registrationId: string, newStatus: string) => {
    if (onStatusChange) {
      onStatusChange(registrationId, newStatus);
      message.success(`Registration status updated to ${newStatus}`);
    }
  };

  const handleDeleteRegistration = (registrationId: string) => {
    if (onDeleteRegistration) {
      onDeleteRegistration(registrationId);
      message.success('Registration deleted successfully');
    }
  };

  const handleExport = () => {
    if (onExportRegistrations) {
      onExportRegistrations();
    } else {
      // Fallback: export as CSV
      const csvContent = [
        ['Name', 'Email', 'Status', 'Registration Date', 'Notes'].join(','),
        ...filteredRegistrations.map(reg => [
          reg.userName,
          reg.userEmail,
          reg.status,
          moment(reg.registrationDate).format('YYYY-MM-DD HH:mm'),
          reg.notes || ''
        ].map(field => `"${field}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${event?.title || 'event'}-registrations.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'userName',
      key: 'userName',
      sorter: (a: Registration, b: Registration) => a.userName.localeCompare(b.userName),
      render: (text: string) => (
        <Space>
          <UserOutlined />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'userEmail',
      key: 'userEmail',
      render: (email: string) => (
        <Space>
          <MailOutlined />
          <a href={`mailto:${email}`}>{email}</a>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: 'Confirmed', value: 'confirmed' },
        { text: 'Waitlist', value: 'waitlist' },
        { text: 'Cancelled', value: 'cancelled' },
      ],
      onFilter: (value: any, record: Registration) => record.status === value,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Registration Date',
      dataIndex: 'registrationDate',
      key: 'registrationDate',
      sorter: (a: Registration, b: Registration) => 
        moment(a.registrationDate).unix() - moment(b.registrationDate).unix(),
      render: (date: Date) => moment(date).format('MMM DD, YYYY HH:mm'),
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
      ellipsis: true,
      render: (notes: string) => notes ? (
        <Tooltip title={notes}>
          <Text type="secondary">{notes}</Text>
        </Tooltip>
      ) : (
        <Text type="secondary">-</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: Registration) => (
        <Space>
          <Select
            value={record.status}
            onChange={(value) => handleStatusChange(record._id, value)}
            size="small"
            style={{ width: 100 }}
          >
            <Option value="confirmed">Confirmed</Option>
            <Option value="waitlist">Waitlist</Option>
            <Option value="cancelled">Cancelled</Option>
          </Select>
          <Popconfirm
            title="Delete this registration?"
            description="This action cannot be undone."
            onConfirm={() => handleDeleteRegistration(record._id)}
            okText="Delete"
            cancelText="Cancel"
            okType="danger"
          >
            <Button
              type="text"
              danger
              size="small"
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  if (!event) return null;

  return (
    <Modal
      title={
        <Space>
          <TeamOutlined />
          <span>Registration Management - {event.title}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
        <Button
          key="export"
          icon={<ExportOutlined />}
          onClick={handleExport}
          disabled={filteredRegistrations.length === 0}
        >
          Export
        </Button>,
      ]}
      width={1200}
      style={{ top: 20 }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Statistics */}
        <Card>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="Total Registrations"
                value={stats.total}
                prefix={<UserOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Confirmed"
                value={stats.confirmed}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Waitlist"
                value={stats.waitlist}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Cancelled"
                value={stats.cancelled}
                prefix={<DeleteOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Col>
          </Row>
        </Card>

        {/* Filters */}
        <Card size="small">
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Input
                placeholder="Search by name or email..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </Col>
            <Col>
              <Select
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
              >
                <Option value="all">All Status</Option>
                <Option value="confirmed">Confirmed</Option>
                <Option value="waitlist">Waitlist</Option>
                <Option value="cancelled">Cancelled</Option>
              </Select>
            </Col>
          </Row>
        </Card>

        {/* Registrations Table */}
        <Table
          columns={columns}
          dataSource={filteredRegistrations}
          rowKey="_id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} registrations`,
          }}
          scroll={{ x: 800 }}
          size="small"
        />

        {/* Event Capacity Info */}
        {event.maxAttendees && (
          <Card size="small" title="Capacity Information">
            <Row gutter={16}>
              <Col span={12}>
                <Text>
                  <strong>Maximum Capacity:</strong> {event.maxAttendees} attendees
                </Text>
              </Col>
              <Col span={12}>
                <Text>
                  <strong>Current Registrations:</strong> {stats.confirmed} confirmed
                  {stats.waitlist > 0 && `, ${stats.waitlist} on waitlist`}
                </Text>
              </Col>
            </Row>
            {event.maxAttendees && (
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  {event.maxAttendees - stats.confirmed} spots remaining
                </Text>
              </div>
            )}
          </Card>
        )}
      </Space>
    </Modal>
  );
};

export default RegistrationManagement;
