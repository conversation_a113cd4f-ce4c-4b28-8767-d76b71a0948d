import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { CustomError } from '../utils/errors';
import { logger } from '../utils/logger';

export interface FileUploadResult {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  path: string;
}

export class FileService {
  private uploadDir: string;
  private maxFileSize: number;
  private allowedMimeTypes: string[];

  constructor() {
    this.uploadDir = process.env.UPLOAD_DIR || 'uploads';
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB default
    this.allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/zip',
      'application/x-zip-compressed',
      'text/plain',
      'text/csv',
    ];

    this.ensureUploadDirectory();
  }

  private async ensureUploadDirectory(): Promise<void> {
    try {
      await fs.access(this.uploadDir);
    } catch {
      await fs.mkdir(this.uploadDir, { recursive: true });
      logger.info(`Created upload directory: ${this.uploadDir}`);
    }
  }

  // Configure multer for file uploads
  getMulterConfig(): multer.Multer {
    const storage = multer.diskStorage({
      destination: async (req, file, cb) => {
        try {
          await this.ensureUploadDirectory();
          cb(null, this.uploadDir);
        } catch (error) {
          cb(error as Error, '');
        }
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
        const ext = path.extname(file.originalname);
        const filename = `${uniqueSuffix}${ext}`;
        cb(null, filename);
      },
    });

    const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
      if (this.allowedMimeTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new CustomError(`File type ${file.mimetype} is not allowed`, 400));
      }
    };

    return multer({
      storage,
      fileFilter,
      limits: {
        fileSize: this.maxFileSize,
        files: 10, // Maximum 10 files per request
      },
    });
  }

  // Process uploaded files
  async processUploadedFiles(files: Express.Multer.File[]): Promise<FileUploadResult[]> {
    try {
      const results: FileUploadResult[] = [];

      for (const file of files) {
        const result: FileUploadResult = {
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          url: `/uploads/${file.filename}`,
          path: file.path,
        };

        results.push(result);

        logger.info('File uploaded successfully', {
          filename: file.filename,
          originalName: file.originalname,
          size: file.size,
          mimetype: file.mimetype,
        });
      }

      return results;
    } catch (error) {
      logger.error('Error processing uploaded files', error);
      throw new CustomError('Failed to process uploaded files', 500);
    }
  }

  // Delete a file
  async deleteFile(filename: string): Promise<void> {
    try {
      const filePath = path.join(this.uploadDir, filename);
      await fs.unlink(filePath);
      logger.info('File deleted successfully', { filename });
    } catch (error) {
      logger.error('Error deleting file', { filename, error });
      throw new CustomError('Failed to delete file', 500);
    }
  }

  // Delete multiple files
  async deleteFiles(filenames: string[]): Promise<void> {
    try {
      await Promise.all(filenames.map(filename => this.deleteFile(filename)));
    } catch (error) {
      logger.error('Error deleting files', { filenames, error });
      throw new CustomError('Failed to delete files', 500);
    }
  }

  // Get file information
  async getFileInfo(filename: string): Promise<{
    exists: boolean;
    size?: number;
    mimetype?: string;
    url?: string;
  }> {
    try {
      const filePath = path.join(this.uploadDir, filename);
      const stats = await fs.stat(filePath);
      
      return {
        exists: true,
        size: stats.size,
        url: `/uploads/${filename}`,
      };
    } catch (error) {
      return { exists: false };
    }
  }

  // Validate file before upload
  validateFile(file: Express.Multer.File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.maxFileSize) {
      return {
        valid: false,
        error: `File size exceeds maximum limit of ${this.maxFileSize / 1024 / 1024}MB`,
      };
    }

    // Check mime type
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      return {
        valid: false,
        error: `File type ${file.mimetype} is not allowed`,
      };
    }

    // Check filename
    if (!file.originalname || file.originalname.length > 255) {
      return {
        valid: false,
        error: 'Invalid filename',
      };
    }

    return { valid: true };
  }

  // Clean up orphaned files (files not referenced by any event)
  async cleanupOrphanedFiles(): Promise<{ deletedCount: number; errors: string[] }> {
    try {
      const Event = require('../models/Event').default;
      
      // Get all files in upload directory
      const uploadedFiles = await fs.readdir(this.uploadDir);
      
      // Get all file references from events
      const events = await Event.find({}, 'attachments');
      const referencedFiles = new Set<string>();
      
      events.forEach((event: any) => {
        event.attachments.forEach((attachment: any) => {
          if (attachment.url && attachment.url.startsWith('/uploads/')) {
            const filename = attachment.url.replace('/uploads/', '');
            referencedFiles.add(filename);
          }
        });
      });

      // Find orphaned files
      const orphanedFiles = uploadedFiles.filter(file => !referencedFiles.has(file));
      
      const errors: string[] = [];
      let deletedCount = 0;

      // Delete orphaned files
      for (const file of orphanedFiles) {
        try {
          await this.deleteFile(file);
          deletedCount++;
        } catch (error) {
          errors.push(`Failed to delete ${file}: ${error}`);
        }
      }

      logger.info('Cleanup completed', {
        totalFiles: uploadedFiles.length,
        referencedFiles: referencedFiles.size,
        orphanedFiles: orphanedFiles.length,
        deletedCount,
        errors: errors.length,
      });

      return { deletedCount, errors };
    } catch (error) {
      logger.error('Error during file cleanup', error);
      throw new CustomError('Failed to cleanup orphaned files', 500);
    }
  }

  // Get upload statistics
  async getUploadStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    fileTypes: Record<string, number>;
    averageFileSize: number;
  }> {
    try {
      const Event = require('../models/Event').default;
      const events = await Event.find({}, 'attachments');
      
      let totalFiles = 0;
      let totalSize = 0;
      const fileTypes: Record<string, number> = {};

      events.forEach((event: any) => {
        event.attachments.forEach((attachment: any) => {
          totalFiles++;
          totalSize += attachment.size || 0;
          
          const ext = path.extname(attachment.name).toLowerCase();
          fileTypes[ext] = (fileTypes[ext] || 0) + 1;
        });
      });

      return {
        totalFiles,
        totalSize,
        fileTypes,
        averageFileSize: totalFiles > 0 ? totalSize / totalFiles : 0,
      };
    } catch (error) {
      logger.error('Error getting upload stats', error);
      throw new CustomError('Failed to get upload statistics', 500);
    }
  }

  // Generate secure download URL with expiration
  generateSecureUrl(filename: string, expiresIn: number = 3600): string {
    // In a production environment, you might want to use signed URLs
    // For now, we'll return the direct URL
    return `/uploads/${filename}`;
  }

  // Scan file for viruses (placeholder for future implementation)
  async scanFile(filePath: string): Promise<{ clean: boolean; threats?: string[] }> {
    // Placeholder for virus scanning integration
    // You could integrate with ClamAV, VirusTotal API, etc.
    return { clean: true };
  }
}

export const fileService = new FileService();
