import { createGlobalStyle } from "styled-components";
import { media, touchTargets, spacing } from "./breakpoints";

export const GlobalStyles = createGlobalStyle`
  * {
    box-sizing: border-box;
  }

  html {
    font-size: 14px; /* Mobile-first: smaller base font */
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;

    ${media.md} {
      font-size: 16px; /* Larger font on tablets and up */
    }
  }

  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f5f5f5;
    color: #333;
    overflow-x: hidden; /* Prevent horizontal scroll on mobile */

    /* Improve touch scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Ant Design customizations - Mobile First */
  .ant-layout {
    min-height: 100vh;
  }

  .ant-layout-header {
    padding: 0 ${spacing.mobile.lg}; /* Mobile padding */
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 100;
    height: 56px; /* Touch-friendly height */

    ${media.md} {
      padding: 0 ${spacing.desktop.lg}; /* Desktop padding */
      height: 64px;
    }
  }

  .ant-layout-content {
    padding: ${spacing.mobile.lg}; /* Mobile padding */
    background: #f5f5f5;

    ${media.md} {
      padding: ${spacing.desktop.lg}; /* Desktop padding */
    }
  }

  .ant-layout-sider {
    background: #fff;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);

    /* Hide sidebar on mobile by default */
    ${media.maxMd} {
      position: fixed !important;
      z-index: 1000;
      height: 100vh;
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &.ant-layout-sider-collapsed {
        transform: translateX(-100%);
      }

      &.mobile-sider-open {
        transform: translateX(0);
      }
    }
  }

  /* Calendar customizations */
  .rbc-calendar {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    padding: 16px;
  }

  .rbc-event {
    border-radius: 4px;
    border: none;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 500;
  }

  .rbc-event.rbc-selected {
    box-shadow: 0 0 0 2px #1890ff;
  }

  .rbc-toolbar {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .rbc-toolbar button {
    border: 1px solid #d9d9d9;
    background: #fff;
    color: #333;
    padding: 6px 12px;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.2s;
  }

  .rbc-toolbar button:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  .rbc-toolbar button.rbc-active {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
  }

  /* Form customizations */
  .ant-form-item-label > label {
    font-weight: 500;
  }

  .ant-input:focus,
  .ant-input-focused {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  /* Card customizations */
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
  }

  /* Button customizations - Touch Friendly */
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s;
    min-height: ${touchTargets.comfortable}; /* Touch-friendly height */

    ${media.touch} {
      min-height: ${touchTargets.comfortable};
      padding: 0 16px;
    }

    &.ant-btn-sm {
      min-height: ${touchTargets.minimum};
    }

    &.ant-btn-lg {
      min-height: ${touchTargets.large};
    }
  }

  .ant-btn-primary {
    background: #1890ff;
    border-color: #1890ff;
  }

  .ant-btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
  }

  /* Modal customizations */
  .ant-modal {
    border-radius: 8px;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    border-radius: 0 0 8px 8px;
  }

  /* Notification customizations */
  .ant-notification {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .ant-layout-header {
      padding: 0 16px;
    }

    .ant-layout-content {
      padding: 16px;
    }

    .rbc-calendar {
      padding: 12px;
    }

    .rbc-toolbar {
      flex-direction: column;
      gap: 12px;
    }

    .rbc-toolbar-label {
      order: -1;
      text-align: center;
      font-size: 18px;
      font-weight: 600;
    }
  }

  /* Scrollbar customizations */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Loading states */
  .loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  /* Utility classes */
  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  .mb-0 {
    margin-bottom: 0 !important;
  }

  .mt-16 {
    margin-top: 16px;
  }

  .mb-16 {
    margin-bottom: 16px;
  }

  .p-16 {
    padding: 16px;
  }

  .full-width {
    width: 100%;
  }

  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
`;
