import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card, Space, Collapse, Alert } from 'antd';
import {
  ExclamationCircleOutlined,
  ReloadOutlined,
  BugOutlined,
  HomeOutlined,
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showErrorDetails?: boolean;
  enableReporting?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report error to monitoring service
    if (this.props.enableReporting) {
      this.reportError(error, errorInfo);
    }
  }

  reportError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // In a real application, you would send this to your error reporting service
      // like Sentry, LogRocket, Bugsnag, etc.
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      console.log('Error report:', errorReport);
      
      // Example: Send to error reporting service
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport),
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorInfo, errorId } = this.state;
      const { showErrorDetails = false } = this.props;

      return (
        <div style={{ padding: '50px 20px', maxWidth: '800px', margin: '0 auto' }}>
          <Result
            status="error"
            icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            title="Something went wrong"
            subTitle="We're sorry, but something unexpected happened. Please try refreshing the page or contact support if the problem persists."
            extra={[
              <Button
                key="retry"
                type="primary"
                icon={<ReloadOutlined />}
                onClick={this.handleRetry}
              >
                Try Again
              </Button>,
              <Button
                key="reload"
                icon={<ReloadOutlined />}
                onClick={this.handleReload}
              >
                Reload Page
              </Button>,
              <Button
                key="home"
                icon={<HomeOutlined />}
                onClick={this.handleGoHome}
              >
                Go Home
              </Button>,
            ]}
          />

          {errorId && (
            <Alert
              message="Error ID"
              description={
                <Space direction="vertical">
                  <Text code copyable>
                    {errorId}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    Please include this ID when reporting the issue to support.
                  </Text>
                </Space>
              }
              type="info"
              showIcon
              style={{ marginTop: '20px' }}
            />
          )}

          {showErrorDetails && error && (
            <Card
              title={
                <Space>
                  <BugOutlined />
                  <span>Error Details</span>
                </Space>
              }
              style={{ marginTop: '20px' }}
              size="small"
            >
              <Collapse ghost>
                <Panel header="Error Message" key="message">
                  <Text code style={{ whiteSpace: 'pre-wrap' }}>
                    {error.message}
                  </Text>
                </Panel>
                
                {error.stack && (
                  <Panel header="Stack Trace" key="stack">
                    <Text code style={{ whiteSpace: 'pre-wrap', fontSize: '11px' }}>
                      {error.stack}
                    </Text>
                  </Panel>
                )}
                
                {errorInfo?.componentStack && (
                  <Panel header="Component Stack" key="componentStack">
                    <Text code style={{ whiteSpace: 'pre-wrap', fontSize: '11px' }}>
                      {errorInfo.componentStack}
                    </Text>
                  </Panel>
                )}
              </Collapse>
            </Card>
          )}

          <Card
            title="What can you do?"
            style={{ marginTop: '20px' }}
            size="small"
          >
            <Space direction="vertical">
              <Paragraph>
                • <strong>Try again:</strong> Click the "Try Again" button to retry the operation
              </Paragraph>
              <Paragraph>
                • <strong>Reload the page:</strong> Sometimes a simple page refresh can resolve the issue
              </Paragraph>
              <Paragraph>
                • <strong>Check your connection:</strong> Ensure you have a stable internet connection
              </Paragraph>
              <Paragraph>
                • <strong>Contact support:</strong> If the problem persists, please contact our support team with the error ID above
              </Paragraph>
            </Space>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Hook for error reporting in functional components
export const useErrorHandler = () => {
  const reportError = (error: Error, context?: string) => {
    console.error(`Error in ${context || 'component'}:`, error);
    
    // In a real application, report to error monitoring service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    console.log('Error report:', errorReport);
  };

  return { reportError };
};

export default ErrorBoundary;
