import mongoose, { Document, Schema } from "mongoose";
import {
  Event as IEvent,
  EventType,
  EventPriority,
  EventStatus,
  Department,
  RegistrationStatus,
} from "@gbf-calendar/shared";

export interface EventDocument extends Omit<IEvent, "_id">, Document {
  _id: string;
  // Virtual properties
  duration: number;
  isUpcoming: boolean;
  isActive: boolean;
  isPast: boolean;
}

const attachmentSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: [255, "Attachment name cannot exceed 255 characters"],
    },
    url: {
      type: String,
      required: true,
      trim: true,
      match: [
        /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
        "Please enter a valid URL",
      ],
    },
    type: {
      type: String,
      required: true,
      trim: true,
      maxlength: [100, "Attachment type cannot exceed 100 characters"],
    },
    size: {
      type: Number,
      min: [0, "File size cannot be negative"],
      max: [100 * 1024 * 1024, "File size cannot exceed 100MB"], // 100MB limit
    },
  },
  { _id: false }
);

const recurrencePatternSchema = new Schema(
  {
    frequency: {
      type: String,
      enum: ["daily", "weekly", "monthly", "yearly"],
      required: true,
    },
    interval: {
      type: Number,
      required: true,
      min: 1,
    },
    endDate: {
      type: Date,
    },
    daysOfWeek: [
      {
        type: Number,
        min: 0,
        max: 6,
      },
    ],
  },
  { _id: false }
);

const eventSchema = new Schema<EventDocument>(
  {
    title: {
      type: String,
      required: [true, "Event title is required"],
      trim: true,
      maxlength: [200, "Title cannot exceed 200 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [2000, "Description cannot exceed 2000 characters"],
    },
    agenda: {
      type: String,
      trim: true,
      maxlength: [5000, "Agenda cannot exceed 5000 characters"],
    },
    startDate: {
      type: Date,
      required: [true, "Start date is required"],
      validate: {
        validator: function (this: EventDocument, value: Date) {
          // Allow past dates for completed events or when updating
          if (this.status === EventStatus.COMPLETED || !this.isNew) {
            return true;
          }
          // For new events, start date should not be in the past
          return value >= new Date(new Date().setHours(0, 0, 0, 0));
        },
        message: "Start date cannot be in the past",
      },
    },
    endDate: {
      type: Date,
      required: [true, "End date is required"],
      validate: {
        validator: function (this: EventDocument, value: Date) {
          return value >= this.startDate;
        },
        message: "End date must be after start date",
      },
    },
    location: {
      type: String,
      trim: true,
      maxlength: [200, "Location cannot exceed 200 characters"],
    },
    venue: {
      type: String,
      trim: true,
      maxlength: [200, "Venue cannot exceed 200 characters"],
    },
    spoc: {
      type: String,
      trim: true,
      maxlength: [100, "SPOC name cannot exceed 100 characters"],
    },
    contactEmail: {
      type: String,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Please enter a valid email",
      ],
    },
    contactPhone: {
      type: String,
      trim: true,
      match: [/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number"],
    },
    department: {
      type: String,
      enum: Object.values(Department),
      default: Department.OTHER,
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(EventType),
      default: EventType.OTHER,
      required: true,
    },
    priority: {
      type: String,
      enum: Object.values(EventPriority),
      default: EventPriority.MEDIUM,
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(EventStatus),
      default: EventStatus.DRAFT,
      required: true,
    },
    maxAttendees: {
      type: Number,
      min: [1, "Maximum attendees must be at least 1"],
      max: [10000, "Maximum attendees cannot exceed 10,000"],
    },
    currentAttendees: {
      type: Number,
      min: [0, "Current attendees cannot be negative"],
      default: 0,
    },
    registrationRequired: {
      type: Boolean,
      default: false,
    },
    registrationStatus: {
      type: String,
      enum: Object.values(RegistrationStatus),
      default: RegistrationStatus.OPEN,
    },
    registrationDeadline: {
      type: Date,
      validate: {
        validator: function (this: EventDocument, value: Date) {
          if (!this.registrationRequired || !value) return true;
          return value <= this.startDate;
        },
        message: "Registration deadline must be before event start date",
      },
    },
    registrationLink: {
      type: String,
      trim: true,
      match: [
        /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
        "Please enter a valid URL",
      ],
    },
    waitlistEnabled: {
      type: Boolean,
      default: false,
    },
    tags: [
      {
        type: String,
        trim: true,
        maxlength: [50, "Tag cannot exceed 50 characters"],
      },
    ],
    attachments: [attachmentSchema],
    createdBy: {
      type: String,
      required: [true, "Created by user is required"],
    },
    isRecurring: {
      type: Boolean,
      default: false,
    },
    recurrencePattern: recurrencePatternSchema,
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete (ret as any).__v;
        return ret;
      },
    },
  }
);

// Indexes for better query performance
eventSchema.index({ startDate: 1 });
eventSchema.index({ endDate: 1 });
eventSchema.index({ status: 1 });
eventSchema.index({ type: 1 });
eventSchema.index({ priority: 1 });
eventSchema.index({ createdBy: 1 });
eventSchema.index({ tags: 1 });
eventSchema.index({ title: "text", description: "text", agenda: "text" });

// Compound indexes
eventSchema.index({ startDate: 1, endDate: 1 });
eventSchema.index({ status: 1, startDate: 1 });
eventSchema.index({ type: 1, startDate: 1 });

// Virtual for duration in hours
eventSchema.virtual("durationHours").get(function () {
  const diffMs = this.endDate.getTime() - this.startDate.getTime();
  return Math.round((diffMs / (1000 * 60 * 60)) * 100) / 100; // Round to 2 decimal places
});

// Virtual for checking if event is upcoming
eventSchema.virtual("isUpcoming").get(function () {
  return this.startDate > new Date();
});

// Virtual for checking if event is ongoing
eventSchema.virtual("isOngoing").get(function () {
  const now = new Date();
  return this.startDate <= now && this.endDate >= now;
});

// Virtual for checking if event is past
eventSchema.virtual("isPast").get(function () {
  return this.endDate < new Date();
});

// Ensure virtual fields are serialized
eventSchema.set("toJSON", {
  virtuals: true,
  transform: function (doc, ret) {
    delete (ret as any).__v;
    return ret;
  },
});

// Pre-save middleware for validation
eventSchema.pre("save", function (next) {
  // Validate recurrence pattern if event is recurring
  if (this.isRecurring && !this.recurrencePattern) {
    return next(
      new Error("Recurrence pattern is required for recurring events")
    );
  }

  // Validate registration deadline if registration is required
  if (
    this.registrationRequired &&
    this.registrationDeadline &&
    this.registrationDeadline > this.startDate
  ) {
    return next(
      new Error("Registration deadline must be before event start date")
    );
  }

  // Validate current attendees doesn't exceed max attendees
  if (this.maxAttendees && this.currentAttendees > this.maxAttendees) {
    return next(new Error("Current attendees cannot exceed maximum capacity"));
  }

  // Auto-update registration status based on capacity
  if (this.registrationRequired && this.maxAttendees) {
    if (this.currentAttendees >= this.maxAttendees) {
      this.registrationStatus = this.waitlistEnabled
        ? RegistrationStatus.WAITLIST
        : RegistrationStatus.FULL;
    } else if (
      this.registrationStatus === RegistrationStatus.FULL ||
      this.registrationStatus === RegistrationStatus.WAITLIST
    ) {
      this.registrationStatus = RegistrationStatus.OPEN;
    }
  }

  // Validate end date is after start date
  if (this.endDate <= this.startDate) {
    return next(new Error("End date must be after start date"));
  }

  next();
});

export const Event = mongoose.model<EventDocument>("Event", eventSchema);
