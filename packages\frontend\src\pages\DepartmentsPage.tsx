import React, { useState, useEffect } from 'react';
import { Typography, Spin } from 'antd';
import { Helmet } from 'react-helmet-async';
import moment from 'moment';
import { config } from '@/utils/config';
import { useEvents } from '@/hooks/useEvents';
import { Department, Event } from '@gbf-calendar/shared';
import DepartmentOverview from '@/components/departments/DepartmentOverview';
import DepartmentEvents from '@/components/departments/DepartmentEvents';

const { Title } = Typography;

interface DepartmentStats {
  department: Department;
  totalEvents: number;
  upcomingEvents: number;
  completedEvents: number;
  cancelledEvents: number;
  averageAttendance: number;
  topEvent?: {
    title: string;
    attendees: number;
  };
  recentActivity: number;
  trend: 'up' | 'down' | 'stable';
}

const DepartmentsPage: React.FC = () => {
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [dateRange, setDateRange] = useState<[moment.Moment | null, moment.Moment | null]>([
    moment().subtract(3, 'months'),
    moment().add(3, 'months'),
  ]);

  // Fetch all events for department analysis
  const { data: eventsData, isLoading } = useEvents({
    page: 1,
    limit: 1000, // Get all events for analysis
    filters: {
      startDate: dateRange[0]?.toDate(),
      endDate: dateRange[1]?.toDate(),
    },
  });

  const events = eventsData?.events || [];

  // Calculate department statistics
  const calculateDepartmentStats = (): DepartmentStats[] => {
    const departmentMap = new Map<Department, Event[]>();

    // Group events by department
    events.forEach(event => {
      const dept = event.department;
      if (!departmentMap.has(dept)) {
        departmentMap.set(dept, []);
      }
      departmentMap.get(dept)!.push(event);
    });

    // Calculate stats for each department
    const stats: DepartmentStats[] = [];
    const now = moment();

    // Include all departments, even those without events
    Object.values(Department).forEach(dept => {
      const deptEvents = departmentMap.get(dept) || [];
      
      const upcomingEvents = deptEvents.filter(e => 
        moment(e.startDate).isAfter(now)
      ).length;
      
      const completedEvents = deptEvents.filter(e => 
        e.status === 'completed'
      ).length;
      
      const cancelledEvents = deptEvents.filter(e => 
        e.status === 'cancelled'
      ).length;

      const totalAttendees = deptEvents.reduce((sum, e) => 
        sum + (e.currentAttendees || 0), 0
      );
      
      const averageAttendance = deptEvents.length > 0 
        ? totalAttendees / deptEvents.length 
        : 0;

      // Find top event by attendance
      const topEvent = deptEvents
        .filter(e => e.currentAttendees && e.currentAttendees > 0)
        .sort((a, b) => (b.currentAttendees || 0) - (a.currentAttendees || 0))[0];

      // Calculate recent activity (events in last 30 days)
      const recentActivity = deptEvents.filter(e =>
        moment(e.createdAt).isAfter(moment().subtract(30, 'days'))
      ).length;

      // Simple trend calculation based on recent vs older events
      const recentEvents = deptEvents.filter(e =>
        moment(e.createdAt).isAfter(moment().subtract(30, 'days'))
      ).length;
      const olderEvents = deptEvents.filter(e =>
        moment(e.createdAt).isBetween(
          moment().subtract(60, 'days'),
          moment().subtract(30, 'days')
        )
      ).length;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (recentEvents > olderEvents) trend = 'up';
      else if (recentEvents < olderEvents) trend = 'down';

      stats.push({
        department: dept,
        totalEvents: deptEvents.length,
        upcomingEvents,
        completedEvents,
        cancelledEvents,
        averageAttendance,
        topEvent: topEvent ? {
          title: topEvent.title,
          attendees: topEvent.currentAttendees || 0,
        } : undefined,
        recentActivity,
        trend,
      });
    });

    return stats.sort((a, b) => b.totalEvents - a.totalEvents);
  };

  const departmentStats = calculateDepartmentStats();

  const handleViewDepartment = (department: Department) => {
    setSelectedDepartment(department);
  };

  const handleBackToOverview = () => {
    setSelectedDepartment(null);
  };

  const handleDateRangeChange = (dates: [moment.Moment | null, moment.Moment | null]) => {
    setDateRange(dates);
  };

  const handleCreateEvent = () => {
    // Navigate to create event page with department pre-selected
    // This would typically use React Router
    console.log('Create event for department:', selectedDepartment);
  };

  const handleViewEvent = (event: Event) => {
    // Navigate to event detail page
    console.log('View event:', event._id);
  };

  const handleEditEvent = (event: Event) => {
    // Navigate to edit event page
    console.log('Edit event:', event._id);
  };

  // Filter events for selected department
  const departmentEvents = selectedDepartment 
    ? events.filter(event => event.department === selectedDepartment)
    : [];

  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Departments - {config.appName}</title>
        <meta name="description" content="Department-wise event management and analytics" />
      </Helmet>

      <div style={{ padding: '24px' }}>
        {selectedDepartment ? (
          <DepartmentEvents
            department={selectedDepartment}
            events={departmentEvents}
            loading={isLoading}
            onBack={handleBackToOverview}
            onCreateEvent={handleCreateEvent}
            onViewEvent={handleViewEvent}
            onEditEvent={handleEditEvent}
          />
        ) : (
          <DepartmentOverview
            departmentStats={departmentStats}
            loading={isLoading}
            onViewDepartment={handleViewDepartment}
            onDateRangeChange={handleDateRangeChange}
          />
        )}
      </div>
    </>
  );
};

export default DepartmentsPage;
