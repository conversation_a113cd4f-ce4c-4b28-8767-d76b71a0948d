import mongoose, { Document, Schema } from "mongoose";
import { EventRegistration } from "@gbf-calendar/shared";

export interface RegistrationDocument extends Omit<EventRegistration, "_id">, Document {
  _id: string;
}

const registrationSchema = new Schema<RegistrationDocument>(
  {
    eventId: {
      type: String,
      required: [true, "Event ID is required"],
      ref: "Event",
    },
    userId: {
      type: String,
      required: [true, "User ID is required"],
      ref: "User",
    },
    userEmail: {
      type: String,
      required: [true, "User email is required"],
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Please enter a valid email",
      ],
    },
    userName: {
      type: String,
      required: [true, "User name is required"],
      trim: true,
      maxlength: [100, "User name cannot exceed 100 characters"],
    },
    registrationDate: {
      type: Date,
      default: Date.now,
      required: true,
    },
    status: {
      type: String,
      enum: ["confirmed", "waitlist", "cancelled"],
      default: "confirmed",
      required: true,
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [500, "Notes cannot exceed 500 characters"],
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete (ret as any).__v;
        return ret;
      },
    },
  }
);

// Indexes for better query performance
registrationSchema.index({ eventId: 1, userId: 1 }, { unique: true }); // Prevent duplicate registrations
registrationSchema.index({ eventId: 1, status: 1 });
registrationSchema.index({ userId: 1 });
registrationSchema.index({ userEmail: 1 });
registrationSchema.index({ registrationDate: -1 });

// Pre-save middleware for validation
registrationSchema.pre("save", async function (next) {
  // Check if event exists and is still accepting registrations
  const Event = mongoose.model("Event");
  const event = await Event.findById(this.eventId);
  
  if (!event) {
    return next(new Error("Event not found"));
  }

  // Check if registration deadline has passed
  if (event.registrationDeadline && new Date() > event.registrationDeadline) {
    return next(new Error("Registration deadline has passed"));
  }

  // Check if event is cancelled
  if (event.status === "cancelled") {
    return next(new Error("Cannot register for cancelled event"));
  }

  next();
});

// Static methods
registrationSchema.statics.getEventRegistrationCount = function(eventId: string) {
  return this.countDocuments({ eventId, status: { $ne: "cancelled" } });
};

registrationSchema.statics.getConfirmedRegistrationCount = function(eventId: string) {
  return this.countDocuments({ eventId, status: "confirmed" });
};

registrationSchema.statics.getWaitlistCount = function(eventId: string) {
  return this.countDocuments({ eventId, status: "waitlist" });
};

registrationSchema.statics.getUserRegistrations = function(userId: string) {
  return this.find({ userId, status: { $ne: "cancelled" } })
    .populate("eventId")
    .sort({ registrationDate: -1 });
};

const Registration = mongoose.model<RegistrationDocument>("Registration", registrationSchema);

export default Registration;
