// Haptic feedback utilities for mobile devices

export const hapticFeedback = {
  // Light tap feedback
  light: () => {
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }
  },

  // Medium tap feedback
  medium: () => {
    if (navigator.vibrate) {
      navigator.vibrate(20);
    }
  },

  // Heavy tap feedback
  heavy: () => {
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
  },

  // Success feedback
  success: () => {
    if (navigator.vibrate) {
      navigator.vibrate([10, 50, 10]);
    }
  },

  // Error feedback
  error: () => {
    if (navigator.vibrate) {
      navigator.vibrate([50, 100, 50, 100, 50]);
    }
  },

  // Selection feedback
  selection: () => {
    if (navigator.vibrate) {
      navigator.vibrate(5);
    }
  },

  // Check if haptic feedback is supported
  isSupported: () => {
    return "vibrate" in navigator && typeof navigator.vibrate === "function";
  },

  // Custom pattern
  custom: (pattern: number | number[]) => {
    if (navigator.vibrate) {
      navigator.vibrate(pattern);
    }
  },
};

// Hook for using haptic feedback in components
export const useHapticFeedback = () => {
  return hapticFeedback;
};
