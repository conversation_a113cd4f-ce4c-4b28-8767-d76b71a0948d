import React from "react";
import { Helmet } from "react-helmet-async";
import { config } from "@/utils/config";
import ResponsiveCalendar from "@/components/calendar/ResponsiveCalendar";
import { useCalendarStore } from "@/store/calendarStore";

const CalendarPage: React.FC = () => {
  const { setSelectedEvent, setEventModalOpen } = useCalendarStore();

  const handleEventSelect = (event: any) => {
    setSelectedEvent(event._id);
    setEventModalOpen(true);
  };

  const handleSlotSelect = (slotInfo: { start: Date; end: Date }) => {
    // Could open create event modal with pre-filled dates
    console.log("Slot selected:", slotInfo);
  };

  return (
    <>
      <Helmet>
        <title>Calendar - {config.appName}</title>
        <meta
          name="description"
          content="View and manage events in calendar format"
        />
      </Helmet>

      <ResponsiveCalendar
        onEventSelect={handleEventSelect}
        onSlotSelect={handleSlotSelect}
      />
    </>
  );
};

export default CalendarPage;
