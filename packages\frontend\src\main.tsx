import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ConfigProvider } from "antd";
import { HelmetProvider } from "react-helmet-async";
import { ErrorBoundary } from "react-error-boundary";

import App from "./App";
import { config, validateConfig } from "@/utils/config";
import { GlobalStyles } from "@/styles/GlobalStyles";
import { antdTheme } from "@/styles/theme";
import ErrorFallback from "@/components/common/ErrorFallback";
import { registerSW } from "virtual:pwa-register";

// Validate configuration
try {
  validateConfig();
} catch (error) {
  console.error("Configuration validation failed:", error);
}

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.statusCode >= 400 && error?.statusCode < 500) {
          return false;
        }
        return failureCount < 3;
      },
      staleTime: 1 * 60 * 1000, // 1 minute
      gcTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: false,
    },
  },
});

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <ConfigProvider theme={antdTheme}>
            <BrowserRouter>
              <GlobalStyles />
              <App />
              {config.enableQueryDevtools && (
                <ReactQueryDevtools initialIsOpen={false} />
              )}
            </BrowserRouter>
          </ConfigProvider>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  </React.StrictMode>
);

// Register service worker for PWA
const updateSW = registerSW({
  onNeedRefresh() {
    if (confirm("New content available. Reload?")) {
      updateSW(true);
    }
  },
  onOfflineReady() {
    console.log("App ready to work offline");
  },
});
