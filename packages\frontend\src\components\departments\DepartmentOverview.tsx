import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Tag,
  Button,
  Table,
  Progress,
  Avatar,
  Tooltip,
  Select,
  DatePicker,
} from 'antd';
import {
  TeamOutlined,
  CalendarOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { Department, Event, EventStatus, EventPriority } from '@gbf-calendar/shared';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface DepartmentStats {
  department: Department;
  totalEvents: number;
  upcomingEvents: number;
  completedEvents: number;
  cancelledEvents: number;
  averageAttendance: number;
  topEvent?: {
    title: string;
    attendees: number;
  };
  recentActivity: number;
  trend: 'up' | 'down' | 'stable';
}

interface DepartmentOverviewProps {
  departmentStats: DepartmentStats[];
  loading?: boolean;
  onViewDepartment?: (department: Department) => void;
  onDateRangeChange?: (dates: [moment.Moment | null, moment.Moment | null]) => void;
}

const DepartmentOverview: React.FC<DepartmentOverviewProps> = ({
  departmentStats,
  loading = false,
  onViewDepartment,
  onDateRangeChange,
}) => {
  const [sortBy, setSortBy] = useState<'totalEvents' | 'upcomingEvents' | 'averageAttendance'>('totalEvents');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const getDepartmentColor = (department: Department) => {
    const colors = {
      [Department.EXECUTIVE]: 'purple',
      [Department.FINANCE]: 'green',
      [Department.OPERATIONS]: 'blue',
      [Department.HUMAN_RESOURCES]: 'orange',
      [Department.MARKETING]: 'pink',
      [Department.SALES]: 'cyan',
      [Department.TECHNOLOGY]: 'geekblue',
      [Department.LEGAL]: 'red',
      [Department.COMPLIANCE]: 'yellow',
      [Department.STRATEGY]: 'magenta',
      [Department.RESEARCH]: 'lime',
      [Department.CUSTOMER_SERVICE]: 'volcano',
      [Department.ADMINISTRATION]: 'gold',
      [Department.OTHER]: 'default',
    };
    return colors[department] || 'default';
  };

  const formatDepartmentName = (department: Department) => {
    return department.replace(/_/g, ' ').toUpperCase();
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'down':
        return <FallOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  const sortedStats = [...departmentStats].sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
  });

  const totalStats = departmentStats.reduce(
    (acc, dept) => ({
      totalEvents: acc.totalEvents + dept.totalEvents,
      upcomingEvents: acc.upcomingEvents + dept.upcomingEvents,
      completedEvents: acc.completedEvents + dept.completedEvents,
      cancelledEvents: acc.cancelledEvents + dept.cancelledEvents,
    }),
    { totalEvents: 0, upcomingEvents: 0, completedEvents: 0, cancelledEvents: 0 }
  );

  const columns = [
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      render: (department: Department) => (
        <Space>
          <Avatar
            style={{
              backgroundColor: getDepartmentColor(department),
              color: '#fff',
            }}
            size="small"
          >
            {department.charAt(0).toUpperCase()}
          </Avatar>
          <Text strong>{formatDepartmentName(department)}</Text>
        </Space>
      ),
    },
    {
      title: 'Total Events',
      dataIndex: 'totalEvents',
      key: 'totalEvents',
      sorter: true,
      render: (value: number, record: DepartmentStats) => (
        <Space>
          <Text>{value}</Text>
          {getTrendIcon(record.trend)}
        </Space>
      ),
    },
    {
      title: 'Upcoming',
      dataIndex: 'upcomingEvents',
      key: 'upcomingEvents',
      sorter: true,
      render: (value: number) => (
        <Tag color="blue">{value}</Tag>
      ),
    },
    {
      title: 'Completed',
      dataIndex: 'completedEvents',
      key: 'completedEvents',
      render: (value: number) => (
        <Tag color="green">{value}</Tag>
      ),
    },
    {
      title: 'Cancelled',
      dataIndex: 'cancelledEvents',
      key: 'cancelledEvents',
      render: (value: number) => (
        <Tag color="red">{value}</Tag>
      ),
    },
    {
      title: 'Avg. Attendance',
      dataIndex: 'averageAttendance',
      key: 'averageAttendance',
      sorter: true,
      render: (value: number) => (
        <Text>{value.toFixed(1)}</Text>
      ),
    },
    {
      title: 'Top Event',
      dataIndex: 'topEvent',
      key: 'topEvent',
      render: (topEvent?: { title: string; attendees: number }) => (
        topEvent ? (
          <Tooltip title={`${topEvent.attendees} attendees`}>
            <Text ellipsis style={{ maxWidth: 150 }}>
              {topEvent.title}
            </Text>
          </Tooltip>
        ) : (
          <Text type="secondary">-</Text>
        )
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: DepartmentStats) => (
        <Button
          type="text"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => onViewDepartment?.(record.department)}
        >
          View
        </Button>
      ),
    },
  ];

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      {/* Header Controls */}
      <Card size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              Department Overview
            </Title>
          </Col>
          <Col>
            <Space>
              <RangePicker
                onChange={onDateRangeChange}
                placeholder={['Start Date', 'End Date']}
              />
              <Select
                value={`${sortBy}-${sortOrder}`}
                onChange={(value) => {
                  const [field, order] = value.split('-');
                  setSortBy(field as any);
                  setSortOrder(order as 'asc' | 'desc');
                }}
                style={{ width: 180 }}
              >
                <Option value="totalEvents-desc">Most Events</Option>
                <Option value="totalEvents-asc">Least Events</Option>
                <Option value="upcomingEvents-desc">Most Upcoming</Option>
                <Option value="averageAttendance-desc">Highest Attendance</Option>
                <Option value="averageAttendance-asc">Lowest Attendance</Option>
              </Select>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Summary Statistics */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Events"
              value={totalStats.totalEvents}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Upcoming Events"
              value={totalStats.upcomingEvents}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Completed Events"
              value={totalStats.completedEvents}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Departments"
              value={departmentStats.filter(d => d.totalEvents > 0).length}
              suffix={`/ ${departmentStats.length}`}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Department Performance Chart */}
      <Card title="Department Performance">
        <Row gutter={16}>
          {sortedStats.slice(0, 6).map((dept) => (
            <Col span={4} key={dept.department}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Avatar
                  style={{
                    backgroundColor: getDepartmentColor(dept.department),
                    color: '#fff',
                    marginBottom: 8,
                  }}
                  size="large"
                >
                  {dept.department.charAt(0).toUpperCase()}
                </Avatar>
                <div>
                  <Text strong style={{ fontSize: '12px' }}>
                    {formatDepartmentName(dept.department)}
                  </Text>
                </div>
                <div style={{ marginTop: 8 }}>
                  <Progress
                    type="circle"
                    size={60}
                    percent={Math.round((dept.totalEvents / Math.max(...departmentStats.map(d => d.totalEvents))) * 100)}
                    format={() => dept.totalEvents}
                  />
                </div>
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    {dept.upcomingEvents} upcoming
                  </Text>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* Detailed Department Table */}
      <Card title="Department Details">
        <Table
          columns={columns}
          dataSource={sortedStats}
          rowKey="department"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} departments`,
          }}
          onChange={(pagination, filters, sorter: any) => {
            if (sorter.field) {
              setSortBy(sorter.field);
              setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
            }
          }}
        />
      </Card>
    </Space>
  );
};

export default DepartmentOverview;
