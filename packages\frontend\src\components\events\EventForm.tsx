import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  DatePicker,
  Select,
  Switch,
  InputNumber,
  Button,
  Card,
  Row,
  Col,
  Space,
  Upload,
  Tag,
  Divider,
  Alert,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import FileUpload, { FileAttachment } from "@/components/common/FileUpload";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import moment from "moment";
import {
  CreateEventSchema,
  EventType,
  EventPriority,
  EventStatus,
  Department,
  RegistrationStatus,
  CreateEventRequest,
} from "@gbf-calendar/shared";
import { EventFormData } from "@/types";

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface EventFormProps {
  initialData?: Partial<EventFormData>;
  onSubmit: (data: CreateEventRequest) => void;
  onCancel: () => void;
  loading?: boolean;
  mode?: "create" | "edit";
}

const EventForm: React.FC<EventFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = "create",
}) => {
  const [tags, setTags] = useState<string[]>(initialData?.tags || []);
  const [newTag, setNewTag] = useState("");
  const [attachments, setAttachments] = useState<FileAttachment[]>(
    initialData?.attachments || []
  );

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset,
  } = useForm<EventFormData>({
    resolver: zodResolver(CreateEventSchema),
    defaultValues: {
      title: "",
      description: "",
      agenda: "",
      startDate: new Date(),
      endDate: new Date(),
      location: "",
      venue: "",
      spoc: "",
      contactEmail: "",
      contactPhone: "",
      department: Department.OTHER,
      type: EventType.OTHER,
      priority: EventPriority.MEDIUM,
      status: EventStatus.DRAFT,
      maxAttendees: undefined,
      registrationRequired: false,
      registrationStatus: RegistrationStatus.OPEN,
      registrationDeadline: undefined,
      registrationLink: "",
      waitlistEnabled: false,
      tags: [],
      isRecurring: false,
      ...initialData,
    },
  });

  const watchRegistrationRequired = watch("registrationRequired");
  const watchIsRecurring = watch("isRecurring");
  const watchStartDate = watch("startDate");

  useEffect(() => {
    if (initialData) {
      reset(initialData);
      setTags(initialData.tags || []);
      setAttachments(initialData.attachments || []);
    }
  }, [initialData, reset]);

  const handleAddTag = () => {
    if (newTag && !tags.includes(newTag)) {
      const updatedTags = [...tags, newTag];
      setTags(updatedTags);
      setValue("tags", updatedTags);
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(updatedTags);
    setValue("tags", updatedTags);
  };

  const handleFileUpload = (files: FileAttachment[]) => {
    setAttachments(files);
    setValue("attachments", files);
  };

  const onFormSubmit = (data: EventFormData) => {
    const formattedData: CreateEventRequest = {
      ...data,
      tags,
      attachments,
      createdBy: "current-user-id", // This should come from auth context
    };
    onSubmit(formattedData);
  };

  return (
    <Card
      title={`${mode === "create" ? "Create" : "Edit"} Event`}
      className="event-form"
    >
      <Form layout="vertical" onFinish={handleSubmit(onFormSubmit)}>
        {/* Basic Information */}
        <Card
          type="inner"
          title="Basic Information"
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Event Title"
                required
                validateStatus={errors.title ? "error" : ""}
                help={errors.title?.message}
              >
                <Controller
                  name="title"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="Enter event title"
                      maxLength={200}
                      showCount
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Department"
                validateStatus={errors.department ? "error" : ""}
                help={errors.department?.message}
              >
                <Controller
                  name="department"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} placeholder="Select department">
                      {Object.values(Department).map((dept) => (
                        <Option key={dept} value={dept}>
                          {dept.replace(/_/g, " ").toUpperCase()}
                        </Option>
                      ))}
                    </Select>
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Event Type"
                validateStatus={errors.type ? "error" : ""}
                help={errors.type?.message}
              >
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} placeholder="Select event type">
                      {Object.values(EventType).map((type) => (
                        <Option key={type} value={type}>
                          {type.replace(/_/g, " ").toUpperCase()}
                        </Option>
                      ))}
                    </Select>
                  )}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Priority"
                validateStatus={errors.priority ? "error" : ""}
                help={errors.priority?.message}
              >
                <Controller
                  name="priority"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} placeholder="Select priority">
                      {Object.values(EventPriority).map((priority) => (
                        <Option key={priority} value={priority}>
                          {priority.toUpperCase()}
                        </Option>
                      ))}
                    </Select>
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Status"
                validateStatus={errors.status ? "error" : ""}
                help={errors.status?.message}
              >
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} placeholder="Select status">
                      {Object.values(EventStatus).map((status) => (
                        <Option key={status} value={status}>
                          {status.toUpperCase()}
                        </Option>
                      ))}
                    </Select>
                  )}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Description"
            validateStatus={errors.description ? "error" : ""}
            help={errors.description?.message}
          >
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextArea
                  {...field}
                  placeholder="Enter event description"
                  rows={4}
                  maxLength={2000}
                  showCount
                />
              )}
            />
          </Form.Item>

          <Form.Item
            label="Agenda"
            validateStatus={errors.agenda ? "error" : ""}
            help={errors.agenda?.message}
          >
            <Controller
              name="agenda"
              control={control}
              render={({ field }) => (
                <TextArea
                  {...field}
                  placeholder="Enter event agenda"
                  rows={6}
                  maxLength={5000}
                  showCount
                />
              )}
            />
          </Form.Item>
        </Card>

        {/* Date and Time */}
        <Card type="inner" title="Date & Time" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Start Date & Time"
                required
                validateStatus={errors.startDate ? "error" : ""}
                help={errors.startDate?.message}
              >
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      {...field}
                      value={field.value ? moment(field.value) : null}
                      onChange={(date) => field.onChange(date?.toDate())}
                      showTime
                      format="YYYY-MM-DD HH:mm"
                      style={{ width: "100%" }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="End Date & Time"
                required
                validateStatus={errors.endDate ? "error" : ""}
                help={errors.endDate?.message}
              >
                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      {...field}
                      value={field.value ? moment(field.value) : null}
                      onChange={(date) => field.onChange(date?.toDate())}
                      showTime
                      format="YYYY-MM-DD HH:mm"
                      style={{ width: "100%" }}
                      disabledDate={(current) =>
                        current &&
                        watchStartDate &&
                        current.isBefore(moment(watchStartDate))
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Location */}
        <Card type="inner" title="Location" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Location"
                validateStatus={errors.location ? "error" : ""}
                help={errors.location?.message}
              >
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} placeholder="Enter location" />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Venue"
                validateStatus={errors.venue ? "error" : ""}
                help={errors.venue?.message}
              >
                <Controller
                  name="venue"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} placeholder="Enter venue details" />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Contact Information */}
        <Card
          type="inner"
          title="Contact Information"
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="SPOC (Single Point of Contact)"
                validateStatus={errors.spoc ? "error" : ""}
                help={errors.spoc?.message}
              >
                <Controller
                  name="spoc"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} placeholder="Enter SPOC name" />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Contact Email"
                validateStatus={errors.contactEmail ? "error" : ""}
                help={errors.contactEmail?.message}
              >
                <Controller
                  name="contactEmail"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter contact email"
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Contact Phone"
                validateStatus={errors.contactPhone ? "error" : ""}
                help={errors.contactPhone?.message}
              >
                <Controller
                  name="contactPhone"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} placeholder="Enter contact phone" />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Registration Settings */}
        <Card
          type="inner"
          title="Registration Settings"
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item>
                <Controller
                  name="registrationRequired"
                  control={control}
                  render={({ field }) => (
                    <Switch
                      {...field}
                      checked={field.value}
                      checkedChildren="Registration Required"
                      unCheckedChildren="No Registration"
                    />
                  )}
                />
                <Tooltip title="Enable this if attendees need to register for the event">
                  <InfoCircleOutlined
                    style={{ marginLeft: 8, color: "#1890ff" }}
                  />
                </Tooltip>
              </Form.Item>
            </Col>
          </Row>

          {watchRegistrationRequired && (
            <>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="Maximum Attendees"
                    validateStatus={errors.maxAttendees ? "error" : ""}
                    help={errors.maxAttendees?.message}
                  >
                    <Controller
                      name="maxAttendees"
                      control={control}
                      render={({ field }) => (
                        <InputNumber
                          {...field}
                          min={1}
                          max={10000}
                          placeholder="Enter max attendees"
                          style={{ width: "100%" }}
                        />
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Registration Deadline"
                    validateStatus={errors.registrationDeadline ? "error" : ""}
                    help={errors.registrationDeadline?.message}
                  >
                    <Controller
                      name="registrationDeadline"
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          {...field}
                          value={field.value ? moment(field.value) : null}
                          onChange={(date) => field.onChange(date?.toDate())}
                          showTime
                          format="YYYY-MM-DD HH:mm"
                          style={{ width: "100%" }}
                          disabledDate={(current) =>
                            current &&
                            watchStartDate &&
                            current.isAfter(moment(watchStartDate))
                          }
                        />
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Registration Status"
                    validateStatus={errors.registrationStatus ? "error" : ""}
                    help={errors.registrationStatus?.message}
                  >
                    <Controller
                      name="registrationStatus"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          placeholder="Select registration status"
                        >
                          {Object.values(RegistrationStatus).map((status) => (
                            <Option key={status} value={status}>
                              {status.toUpperCase()}
                            </Option>
                          ))}
                        </Select>
                      )}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Registration Link"
                    validateStatus={errors.registrationLink ? "error" : ""}
                    help={errors.registrationLink?.message}
                  >
                    <Controller
                      name="registrationLink"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter external registration link (optional)"
                        />
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item>
                    <Controller
                      name="waitlistEnabled"
                      control={control}
                      render={({ field }) => (
                        <Switch
                          {...field}
                          checked={field.value}
                          checkedChildren="Waitlist Enabled"
                          unCheckedChildren="No Waitlist"
                        />
                      )}
                    />
                    <Tooltip title="Allow users to join a waitlist when event is full">
                      <InfoCircleOutlined
                        style={{ marginLeft: 8, color: "#1890ff" }}
                      />
                    </Tooltip>
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}
        </Card>

        {/* Tags */}
        <Card type="inner" title="Tags" style={{ marginBottom: 16 }}>
          <Form.Item label="Event Tags">
            <Space direction="vertical" style={{ width: "100%" }}>
              <Space>
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add a tag"
                  onPressEnter={handleAddTag}
                />
                <Button
                  type="dashed"
                  onClick={handleAddTag}
                  icon={<PlusOutlined />}
                >
                  Add Tag
                </Button>
              </Space>
              <div>
                {tags.map((tag) => (
                  <Tag
                    key={tag}
                    closable
                    onClose={() => handleRemoveTag(tag)}
                    style={{ marginBottom: 8 }}
                  >
                    {tag}
                  </Tag>
                ))}
              </div>
            </Space>
          </Form.Item>
        </Card>

        {/* Attachments */}
        <Card type="inner" title="Attachments" style={{ marginBottom: 16 }}>
          <Form.Item label="Event Materials">
            <FileUpload
              value={attachments}
              onChange={handleFileUpload}
              maxFiles={10}
              maxFileSize={10}
              showPreview={true}
            />
          </Form.Item>
        </Card>

        {/* Recurring Events */}
        <Card type="inner" title="Recurrence" style={{ marginBottom: 16 }}>
          <Form.Item>
            <Controller
              name="isRecurring"
              control={control}
              render={({ field }) => (
                <Switch
                  {...field}
                  checked={field.value}
                  checkedChildren="Recurring Event"
                  unCheckedChildren="One-time Event"
                />
              )}
            />
          </Form.Item>

          {watchIsRecurring && (
            <Alert
              message="Recurring Event Settings"
              description="Recurring event functionality will be available in the next update. For now, please create individual events."
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </Card>

        {/* Form Actions */}
        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
            >
              {mode === "create" ? "Create Event" : "Update Event"}
            </Button>
            <Button onClick={onCancel} size="large">
              Cancel
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default EventForm;
