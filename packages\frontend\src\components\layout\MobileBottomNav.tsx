import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import styled from "styled-components";
import { hapticFeedback } from "@/utils/haptics";
import {
  DashboardOutlined,
  CalendarOutlined,
  UnorderedListOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { media, touchTargets } from "@/styles/breakpoints";

const BottomNavContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0;
  z-index: 1000;

  /* Only show on mobile */
  ${media.md} {
    display: none;
  }

  /* Add safe area padding for iOS */
  padding-bottom: max(8px, env(safe-area-inset-bottom));
`;

const NavItem = styled.div<{ active: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: ${touchTargets.comfortable};
  min-width: ${touchTargets.large};
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 8px;
  padding: 4px 8px;

  color: ${(props) => (props.active ? "#1890ff" : "#8c8c8c")};

  &:active {
    background: rgba(24, 144, 255, 0.1);
    transform: scale(0.95);
  }

  .nav-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .nav-label {
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    line-height: 1;
  }
`;

interface MobileBottomNavProps {
  className?: string;
}

const MobileBottomNav: React.FC<MobileBottomNavProps> = ({ className }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = [
    {
      key: "/dashboard",
      icon: <DashboardOutlined className="nav-icon" />,
      label: "Dashboard",
      path: "/dashboard",
    },
    {
      key: "/calendar",
      icon: <CalendarOutlined className="nav-icon" />,
      label: "Calendar",
      path: "/calendar",
    },
    {
      key: "/events",
      icon: <UnorderedListOutlined className="nav-icon" />,
      label: "Events",
      path: "/events",
    },
    {
      key: "/profile",
      icon: <UserOutlined className="nav-icon" />,
      label: "Profile",
      path: "/profile",
    },
  ];

  const handleNavClick = (path: string) => {
    hapticFeedback.light();
    navigate(path);
  };

  return (
    <BottomNavContainer className={className}>
      {navItems.map((item) => (
        <NavItem
          key={item.key}
          active={location.pathname === item.path}
          onClick={() => handleNavClick(item.path)}
        >
          {item.icon}
          <span className="nav-label">{item.label}</span>
        </NavItem>
      ))}
    </BottomNavContainer>
  );
};

export default MobileBottomNav;
