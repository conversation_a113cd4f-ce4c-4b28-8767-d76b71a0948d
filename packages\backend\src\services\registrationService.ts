import { CreateRegistrationRequest, EventRegistration } from "@gbf-calendar/shared";
import Registration, { RegistrationDocument } from "../models/Registration";
import { Event } from "../models/Event";
import { CustomError } from "../utils/errors";
import { logger } from "../utils/logger";
import { logDbOperation } from "../utils/database";

export class RegistrationService {
  // Register user for an event
  async registerForEvent(
    registrationData: CreateRegistrationRequest
  ): Promise<RegistrationDocument> {
    try {
      // Check if event exists
      const event = await Event.findById(registrationData.eventId);
      if (!event) {
        throw new CustomError("Event not found", 404);
      }

      // Check if event is published and not cancelled
      if (event.status === "cancelled") {
        throw new CustomError("Cannot register for cancelled event", 400);
      }

      if (event.status === "draft") {
        throw new CustomError("Cannot register for unpublished event", 400);
      }

      // Check if registration is required
      if (!event.registrationRequired) {
        throw new CustomError("This event does not require registration", 400);
      }

      // Check if registration deadline has passed
      if (event.registrationDeadline && new Date() > event.registrationDeadline) {
        throw new CustomError("Registration deadline has passed", 400);
      }

      // Check if user is already registered
      const existingRegistration = await Registration.findOne({
        eventId: registrationData.eventId,
        userId: registrationData.userId,
        status: { $ne: "cancelled" },
      });

      if (existingRegistration) {
        throw new CustomError("User is already registered for this event", 400);
      }

      // Check capacity and determine registration status
      let registrationStatus = "confirmed";
      const confirmedCount = await Registration.getConfirmedRegistrationCount(
        registrationData.eventId
      );

      if (event.maxAttendees && confirmedCount >= event.maxAttendees) {
        if (event.waitlistEnabled) {
          registrationStatus = "waitlist";
        } else {
          throw new CustomError("Event is full and waitlist is not enabled", 400);
        }
      }

      // Create registration
      const registration = new Registration({
        ...registrationData,
        status: registrationStatus,
      });

      await registration.save();

      // Update event's current attendees count
      if (registrationStatus === "confirmed") {
        await Event.findByIdAndUpdate(
          registrationData.eventId,
          { $inc: { currentAttendees: 1 } }
        );
      }

      logDbOperation("create", "registrations", {
        registrationId: registration._id,
        eventId: registration.eventId,
        userId: registration.userId,
        status: registration.status,
      });

      return registration;
    } catch (error) {
      logger.error("Register for event service error", error);
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError("Failed to register for event", 500);
    }
  }

  // Cancel registration
  async cancelRegistration(
    eventId: string,
    userId: string
  ): Promise<RegistrationDocument> {
    try {
      const registration = await Registration.findOne({
        eventId,
        userId,
        status: { $ne: "cancelled" },
      });

      if (!registration) {
        throw new CustomError("Registration not found", 404);
      }

      const wasConfirmed = registration.status === "confirmed";
      registration.status = "cancelled";
      await registration.save();

      // Update event's current attendees count if was confirmed
      if (wasConfirmed) {
        await Event.findByIdAndUpdate(eventId, { $inc: { currentAttendees: -1 } });

        // Check if someone from waitlist can be promoted
        await this.promoteFromWaitlist(eventId);
      }

      logDbOperation("update", "registrations", {
        registrationId: registration._id,
        action: "cancelled",
      });

      return registration;
    } catch (error) {
      logger.error("Cancel registration service error", error);
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError("Failed to cancel registration", 500);
    }
  }

  // Get event registrations
  async getEventRegistrations(
    eventId: string,
    status?: string
  ): Promise<RegistrationDocument[]> {
    try {
      const query: any = { eventId };
      if (status) {
        query.status = status;
      }

      const registrations = await Registration.find(query)
        .populate("userId", "firstName lastName email")
        .sort({ registrationDate: 1 });

      return registrations;
    } catch (error) {
      logger.error("Get event registrations service error", error);
      throw new CustomError("Failed to get event registrations", 500);
    }
  }

  // Get user registrations
  async getUserRegistrations(userId: string): Promise<RegistrationDocument[]> {
    try {
      const registrations = await Registration.find({
        userId,
        status: { $ne: "cancelled" },
      })
        .populate("eventId")
        .sort({ registrationDate: -1 });

      return registrations;
    } catch (error) {
      logger.error("Get user registrations service error", error);
      throw new CustomError("Failed to get user registrations", 500);
    }
  }

  // Promote from waitlist when spot becomes available
  private async promoteFromWaitlist(eventId: string): Promise<void> {
    try {
      const event = await Event.findById(eventId);
      if (!event || !event.maxAttendees) return;

      const confirmedCount = await Registration.getConfirmedRegistrationCount(eventId);
      
      if (confirmedCount < event.maxAttendees) {
        // Find oldest waitlist registration
        const waitlistRegistration = await Registration.findOne({
          eventId,
          status: "waitlist",
        }).sort({ registrationDate: 1 });

        if (waitlistRegistration) {
          waitlistRegistration.status = "confirmed";
          await waitlistRegistration.save();

          await Event.findByIdAndUpdate(eventId, { $inc: { currentAttendees: 1 } });

          logger.info("Promoted user from waitlist", {
            registrationId: waitlistRegistration._id,
            eventId,
            userId: waitlistRegistration.userId,
          });
        }
      }
    } catch (error) {
      logger.error("Promote from waitlist error", error);
    }
  }

  // Get registration statistics
  async getRegistrationStats(eventId?: string): Promise<any> {
    try {
      const matchStage = eventId ? { eventId } : {};

      const stats = await Registration.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: "$status",
            count: { $sum: 1 },
          },
        },
      ]);

      const result = {
        total: 0,
        confirmed: 0,
        waitlist: 0,
        cancelled: 0,
      };

      stats.forEach((stat) => {
        result.total += stat.count;
        result[stat._id as keyof typeof result] = stat.count;
      });

      return result;
    } catch (error) {
      logger.error("Get registration stats service error", error);
      throw new CustomError("Failed to get registration statistics", 500);
    }
  }
}

export const registrationService = new RegistrationService();
