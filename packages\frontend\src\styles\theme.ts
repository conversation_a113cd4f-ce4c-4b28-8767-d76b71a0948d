import { ThemeConfig } from "antd";

// Color constants (temporarily defined here until shared package import is fixed)
const EVENT_TYPE_COLORS = {
  meeting: "#1890ff",
  workshop: "#52c41a",
  conference: "#722ed1",
  training: "#fa8c16",
  social: "#eb2f96",
  announcement: "#13c2c2",
  other: "#8c8c8c",
} as const;

const PRIORITY_COLORS = {
  low: "#52c41a",
  medium: "#faad14",
  high: "#fa8c16",
  urgent: "#ff4d4f",
} as const;

const STATUS_COLORS = {
  draft: "#8c8c8c",
  published: "#52c41a",
  cancelled: "#ff4d4f",
  completed: "#1890ff",
} as const;

// Ant Design theme configuration
export const antdTheme: ThemeConfig = {
  token: {
    // Primary colors
    colorPrimary: "#1890ff",
    colorSuccess: "#52c41a",
    colorWarning: "#faad14",
    colorError: "#ff4d4f",
    colorInfo: "#1890ff",

    // Border radius
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,

    // Font
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
    fontSize: 14,
    fontSizeLG: 16,
    fontSizeSM: 12,

    // Spacing
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,

    // Layout
    controlHeight: 32,
    controlHeightLG: 40,
    controlHeightSM: 24,

    // Box shadow
    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
    boxShadowSecondary: "0 4px 12px rgba(0, 0, 0, 0.15)",

    // Colors
    colorBgContainer: "#ffffff",
    colorBgElevated: "#ffffff",
    colorBgLayout: "#f5f5f5",
    colorBorder: "#d9d9d9",
    colorBorderSecondary: "#f0f0f0",
    colorText: "#333333",
    colorTextSecondary: "#666666",
    colorTextTertiary: "#999999",
    colorTextQuaternary: "#cccccc",
  },
  components: {
    Layout: {
      headerBg: "#ffffff",
      siderBg: "#ffffff",
      bodyBg: "#f5f5f5",
    },
    Menu: {
      itemBg: "transparent",
      itemSelectedBg: "#e6f7ff",
      itemSelectedColor: "#1890ff",
      itemHoverBg: "#f5f5f5",
    },
    Button: {
      borderRadius: 6,
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
    },
    Input: {
      borderRadius: 6,
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
    },
    Card: {
      borderRadius: 8,
      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
    },
    Modal: {
      borderRadius: 8,
    },
    Notification: {
      borderRadius: 8,
    },
    Table: {
      borderRadius: 8,
      headerBg: "#fafafa",
    },
    Tabs: {
      borderRadius: 6,
    },
  },
};

// Event type colors for calendar and UI
export const eventTypeColors = EVENT_TYPE_COLORS;
export const priorityColors = PRIORITY_COLORS;
export const statusColors = STATUS_COLORS;

// Additional theme utilities
export const theme = {
  colors: {
    primary: "#1890ff",
    success: "#52c41a",
    warning: "#faad14",
    error: "#ff4d4f",
    info: "#1890ff",
    text: {
      primary: "#333333",
      secondary: "#666666",
      tertiary: "#999999",
      quaternary: "#cccccc",
    },
    background: {
      primary: "#ffffff",
      secondary: "#f5f5f5",
      tertiary: "#fafafa",
    },
    border: {
      primary: "#d9d9d9",
      secondary: "#f0f0f0",
    },
  },
  spacing: {
    xs: "4px",
    sm: "8px",
    md: "12px",
    lg: "16px",
    xl: "24px",
    xxl: "32px",
  },
  borderRadius: {
    sm: "4px",
    md: "6px",
    lg: "8px",
  },
  shadows: {
    sm: "0 1px 3px rgba(0, 0, 0, 0.12)",
    md: "0 2px 8px rgba(0, 0, 0, 0.06)",
    lg: "0 4px 12px rgba(0, 0, 0, 0.15)",
    xl: "0 8px 24px rgba(0, 0, 0, 0.12)",
  },
  breakpoints: {
    xs: "480px",
    sm: "576px",
    md: "768px",
    lg: "992px",
    xl: "1200px",
    xxl: "1600px",
  },
};

// Styled-components theme
export const styledTheme = theme;
