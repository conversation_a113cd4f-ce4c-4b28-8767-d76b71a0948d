import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { registrationService } from '@/services/registrationService';
import { useNotificationActions } from '@/store/notificationStore';

// Query keys
export const registrationKeys = {
  all: ['registrations'] as const,
  lists: () => [...registrationKeys.all, 'list'] as const,
  list: (filters: any) => [...registrationKeys.lists(), filters] as const,
  details: () => [...registrationKeys.all, 'detail'] as const,
  detail: (id: string) => [...registrationKeys.details(), id] as const,
  userRegistrations: (userId: string) => [...registrationKeys.all, 'user', userId] as const,
  eventRegistrations: (eventId: string) => [...registrationKeys.all, 'event', eventId] as const,
  registrationStatus: (eventId: string, userId: string) => 
    [...registrationKeys.all, 'status', eventId, userId] as const,
  stats: (eventId?: string) => [...registrationKeys.all, 'stats', eventId] as const,
};

// Mutation options interface
interface MutationOptions {
  onSuccess?: (data: any, variables: any) => void;
  onError?: (error: any, variables: any) => void;
  onSettled?: () => void;
}

// Get user's registrations
export const useUserRegistrations = (userId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: registrationKeys.userRegistrations(userId),
    queryFn: () => registrationService.getUserRegistrations(userId),
    enabled: enabled && !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get event registrations (admin/editor only)
export const useEventRegistrations = (
  eventId: string, 
  status?: string,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: registrationKeys.eventRegistrations(eventId),
    queryFn: () => registrationService.getEventRegistrations(eventId, status),
    enabled: enabled && !!eventId,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Check registration status for user and event
export const useRegistrationStatus = (
  eventId: string,
  userId: string,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: registrationKeys.registrationStatus(eventId, userId),
    queryFn: () => registrationService.checkRegistrationStatus(eventId, userId),
    enabled: enabled && !!eventId && !!userId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Get registration statistics
export const useRegistrationStats = (eventId?: string) => {
  return useQuery({
    queryKey: registrationKeys.stats(eventId),
    queryFn: () => registrationService.getRegistrationStats(eventId),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Register for event mutation
export const useRegisterForEvent = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();

  return useMutation({
    mutationFn: (data: {
      eventId: string;
      userEmail?: string;
      userName?: string;
      notes?: string;
    }) => registrationService.registerForEvent(data),
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.eventRegistrations(variables.eventId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.userRegistrations(data.userId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.registrationStatus(variables.eventId, data.userId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.stats(variables.eventId) 
      });

      const isWaitlist = data.status === 'waitlist';
      notifications.success(
        isWaitlist ? "Added to Waitlist" : "Registration Successful",
        isWaitlist 
          ? "You've been added to the waitlist. You'll be notified if a spot becomes available."
          : "You have successfully registered for the event."
      );
      
      options?.onSuccess?.(data, variables);
    },
    onError: (error: any, variables) => {
      notifications.error(
        "Registration Failed",
        error.message || "Failed to register for event"
      );
      options?.onError?.(error, variables);
    },
    onSettled: options?.onSettled,
  });
};

// Cancel registration mutation
export const useCancelRegistration = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();

  return useMutation({
    mutationFn: (data: { eventId: string; userId: string }) => 
      registrationService.cancelRegistration(data.eventId, data.userId),
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.eventRegistrations(variables.eventId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.userRegistrations(variables.userId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.registrationStatus(variables.eventId, variables.userId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.stats(variables.eventId) 
      });

      notifications.success(
        "Registration Cancelled",
        "Your registration has been cancelled successfully."
      );
      
      options?.onSuccess?.(data, variables);
    },
    onError: (error: any, variables) => {
      notifications.error(
        "Cancellation Failed",
        error.message || "Failed to cancel registration"
      );
      options?.onError?.(error, variables);
    },
    onSettled: options?.onSettled,
  });
};

// Update registration status mutation (admin/editor only)
export const useUpdateRegistrationStatus = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();

  return useMutation({
    mutationFn: (data: {
      registrationId: string;
      eventId: string;
      status: string;
    }) => registrationService.updateRegistrationStatus(data.registrationId, data.status),
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.eventRegistrations(variables.eventId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.stats(variables.eventId) 
      });

      notifications.success(
        "Status Updated",
        `Registration status updated to ${variables.status}`
      );
      
      options?.onSuccess?.(data, variables);
    },
    onError: (error: any, variables) => {
      notifications.error(
        "Update Failed",
        error.message || "Failed to update registration status"
      );
      options?.onError?.(error, variables);
    },
    onSettled: options?.onSettled,
  });
};

// Delete registration mutation (admin only)
export const useDeleteRegistration = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();

  return useMutation({
    mutationFn: (data: {
      registrationId: string;
      eventId: string;
    }) => registrationService.deleteRegistration(data.registrationId),
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.eventRegistrations(variables.eventId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.stats(variables.eventId) 
      });

      notifications.success(
        "Registration Deleted",
        "Registration has been deleted successfully."
      );
      
      options?.onSuccess?.(data, variables);
    },
    onError: (error: any, variables) => {
      notifications.error(
        "Deletion Failed",
        error.message || "Failed to delete registration"
      );
      options?.onError?.(error, variables);
    },
    onSettled: options?.onSettled,
  });
};

// Bulk operations
export const useBulkUpdateRegistrations = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();

  return useMutation({
    mutationFn: (data: {
      eventId: string;
      registrationIds: string[];
      status: string;
    }) => registrationService.bulkUpdateRegistrations(data.registrationIds, data.status),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.eventRegistrations(variables.eventId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: registrationKeys.stats(variables.eventId) 
      });

      notifications.success(
        "Bulk Update Successful",
        `Updated ${variables.registrationIds.length} registrations`
      );
      
      options?.onSuccess?.(data, variables);
    },
    onError: (error: any, variables) => {
      notifications.error(
        "Bulk Update Failed",
        error.message || "Failed to update registrations"
      );
      options?.onError?.(error, variables);
    },
    onSettled: options?.onSettled,
  });
};
