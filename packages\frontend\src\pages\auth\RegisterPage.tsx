import React from "react";
import { Form, Input, But<PERSON>, Card, Typography, Divider } from "antd";
import { UserOutlined, LockOutlined, MailOutlined } from "@ant-design/icons";
import { Link, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { Helmet } from "react-helmet-async";

import { useRegister } from "@/hooks";
import { CreateUserRequest } from "@/types";
import { config } from "@/utils/config";

const { Title, Text } = Typography;
// const { Option } = Select;

const RegisterContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
`;

const RegisterCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
`;

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const registerMutation = useRegister();

  const handleSubmit = (values: CreateUserRequest) => {
    registerMutation.mutate(values, {
      onSuccess: () => {
        navigate("/dashboard");
      },
    });
  };

  return (
    <>
      <Helmet>
        <title>Register - {config.appName}</title>
        <meta
          name="description"
          content="Create your GBF Events Calendar account"
        />
      </Helmet>

      <RegisterContainer>
        <RegisterCard>
          <div style={{ textAlign: "center", marginBottom: 32 }}>
            <Title level={2} style={{ color: "#1890ff", marginBottom: 8 }}>
              Join {config.appName}
            </Title>
            <Text type="secondary">Create your account to get started</Text>
          </div>

          <Form
            name="register"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: "Please enter your email" },
                { type: "email", message: "Please enter a valid email" },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Enter your email"
                autoComplete="email"
              />
            </Form.Item>

            <div style={{ display: "flex", gap: 16 }}>
              <Form.Item
                name="firstName"
                label="First Name"
                style={{ flex: 1 }}
                rules={[
                  { required: true, message: "Please enter your first name" },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="First name"
                  autoComplete="given-name"
                />
              </Form.Item>

              <Form.Item
                name="lastName"
                label="Last Name"
                style={{ flex: 1 }}
                rules={[
                  { required: true, message: "Please enter your last name" },
                ]}
              >
                <Input placeholder="Last name" autoComplete="family-name" />
              </Form.Item>
            </div>

            <Form.Item name="department" label="Department (Optional)">
              <Input placeholder="Your department" />
            </Form.Item>

            <Form.Item name="position" label="Position (Optional)">
              <Input placeholder="Your position" />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: "Please enter your password" },
                { min: 8, message: "Password must be at least 8 characters" },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Enter your password"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="Confirm Password"
              dependencies={["password"]}
              rules={[
                { required: true, message: "Please confirm your password" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue("password") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error("Passwords do not match"));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Confirm your password"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={registerMutation.isPending}
                block
              >
                Create Account
              </Button>
            </Form.Item>
          </Form>

          <Divider>
            <Text type="secondary">Already have an account?</Text>
          </Divider>

          <div style={{ textAlign: "center" }}>
            <Link to="/login">
              <Button type="link" size="large">
                Sign in instead
              </Button>
            </Link>
          </div>
        </RegisterCard>
      </RegisterContainer>
    </>
  );
};

export default RegisterPage;
