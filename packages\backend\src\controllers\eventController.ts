import { Request, Response } from "express";
import { eventService } from "../services/eventService";
import {
  CreateEventSchema,
  UpdateEventSchema,
  EventSearchParams,
  UserRole,
} from "@gbf-calendar/shared";
import { asyncHandler } from "../middleware/errorHandler";
import { AuthenticatedRequest } from "../middleware/auth";

export class EventController {
  // Create new event
  createEvent = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const eventData = CreateEventSchema.parse(req.body);
      const event = await eventService.createEvent(eventData, req.user._id);

      res.status(201).json({
        success: true,
        data: event,
        message: "Event created successfully",
      });
    }
  );

  // Get event by ID
  getEvent = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const event = await eventService.getEventById(id);

    res.status(200).json({
      success: true,
      data: event,
      message: "Event retrieved successfully",
    });
  });

  // Update event
  updateEvent = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { id } = req.params;
      const updateData = UpdateEventSchema.parse(req.body);

      // Allow admins to edit any event
      let userId = req.user._id;
      if (req.user.role !== UserRole.ADMIN) {
        // For non-admins, the service will check ownership
        userId = req.user._id;
      }

      const event = await eventService.updateEvent(id, updateData, userId);

      res.status(200).json({
        success: true,
        data: event,
        message: "Event updated successfully",
      });
    }
  );

  // Delete event
  deleteEvent = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { id } = req.params;

      // Allow admins to delete any event
      let userId = req.user._id;
      if (req.user.role !== UserRole.ADMIN) {
        // For non-admins, the service will check ownership
        userId = req.user._id;
      }

      await eventService.deleteEvent(id, userId);

      res.status(200).json({
        success: true,
        message: "Event deleted successfully",
      });
    }
  );

  // Get events with filters and pagination
  getEvents = asyncHandler(async (req: Request, res: Response) => {
    const searchParams: EventSearchParams = {
      page: req.query.page ? parseInt(req.query.page as string) : 1,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 20,
      sortBy: (req.query.sortBy as any) || "startDate",
      sortOrder: (req.query.sortOrder as any) || "asc",
      filters: {
        startDate: req.query.startDate
          ? new Date(req.query.startDate as string)
          : undefined,
        endDate: req.query.endDate
          ? new Date(req.query.endDate as string)
          : undefined,
        type: req.query.type
          ? ((req.query.type as string).split(",") as any)
          : undefined,
        status: req.query.status
          ? ((req.query.status as string).split(",") as any)
          : undefined,
        priority: req.query.priority
          ? ((req.query.priority as string).split(",") as any)
          : undefined,
        tags: req.query.tags
          ? (req.query.tags as string).split(",")
          : undefined,
        search: req.query.search as string,
        createdBy: req.query.createdBy as string,
        location: req.query.location as string,
      },
    };

    const result = await eventService.getEvents(searchParams);

    res.status(200).json({
      success: true,
      data: result.events,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
        hasNextPage: result.page < result.totalPages,
        hasPrevPage: result.page > 1,
      },
      message: "Events retrieved successfully",
    });
  });

  // Get events by date range (for calendar view)
  getEventsByDateRange = asyncHandler(async (req: Request, res: Response) => {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        error: "Start date and end date are required",
      });
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({
        success: false,
        error: "Invalid date format",
      });
    }

    const events = await eventService.getEventsByDateRange(start, end);

    return res.status(200).json({
      success: true,
      data: events,
      message: "Events retrieved successfully",
    });
  });

  // Get upcoming events
  getUpcomingEvents = asyncHandler(async (req: Request, res: Response) => {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const events = await eventService.getUpcomingEvents(limit);

    res.status(200).json({
      success: true,
      data: events,
      message: "Upcoming events retrieved successfully",
    });
  });

  // Search events
  searchEvents = asyncHandler(async (req: Request, res: Response) => {
    const { q } = req.query;

    if (!q) {
      return res.status(400).json({
        success: false,
        error: "Search query is required",
      });
    }

    const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
    const events = await eventService.searchEvents(q as string, limit);

    return res.status(200).json({
      success: true,
      data: events,
      message: "Search completed successfully",
    });
  });

  // Get event statistics
  getEventStats = asyncHandler(async (req: Request, res: Response) => {
    const stats = await eventService.getEventStats();

    res.status(200).json({
      success: true,
      data: stats,
      message: "Event statistics retrieved successfully",
    });
  });
}

export const eventController = new EventController();
