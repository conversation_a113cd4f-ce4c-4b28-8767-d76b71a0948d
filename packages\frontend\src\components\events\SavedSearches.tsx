import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Input,
  Modal,
  Space,
  Typography,
  Tag,
  Tooltip,
  Popconfirm,
  Empty,
  message,
} from 'antd';
import {
  SaveOutlined,
  SearchOutlined,
  DeleteOutlined,
  EditOutlined,
  StarOutlined,
  StarFilled,
} from '@ant-design/icons';
import { EventFilters as IEventFilters } from '@gbf-calendar/shared';

const { Text } = Typography;

interface SavedSearch {
  id: string;
  name: string;
  description?: string;
  filters: IEventFilters;
  isFavorite: boolean;
  createdAt: Date;
  lastUsed?: Date;
}

interface SavedSearchesProps {
  currentFilters: IEventFilters;
  onLoadSearch: (filters: IEventFilters) => void;
  onSaveSearch?: (search: Omit<SavedSearch, 'id' | 'createdAt'>) => void;
}

const SavedSearches: React.FC<SavedSearchesProps> = ({
  currentFilters,
  onLoadSearch,
  onSaveSearch,
}) => {
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [searchDescription, setSearchDescription] = useState('');
  const [editingSearch, setEditingSearch] = useState<SavedSearch | null>(null);

  // Load saved searches from localStorage on component mount
  useEffect(() => {
    const saved = localStorage.getItem('eventSavedSearches');
    if (saved) {
      try {
        const searches = JSON.parse(saved);
        setSavedSearches(searches);
      } catch (error) {
        console.error('Error loading saved searches:', error);
      }
    }
  }, []);

  // Save searches to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('eventSavedSearches', JSON.stringify(savedSearches));
  }, [savedSearches]);

  const hasActiveFilters = () => {
    return Object.keys(currentFilters).some(key => {
      const value = currentFilters[key as keyof IEventFilters];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== undefined && value !== null && value !== '';
    });
  };

  const getFilterSummary = (filters: IEventFilters) => {
    const summary: string[] = [];
    
    if (filters.search) summary.push(`Search: "${filters.search}"`);
    if (filters.type && filters.type.length > 0) summary.push(`Types: ${filters.type.length}`);
    if (filters.department && filters.department.length > 0) summary.push(`Departments: ${filters.department.length}`);
    if (filters.status && filters.status.length > 0) summary.push(`Status: ${filters.status.length}`);
    if (filters.priority && filters.priority.length > 0) summary.push(`Priority: ${filters.priority.length}`);
    if (filters.location) summary.push(`Location: "${filters.location}"`);
    if (filters.startDate || filters.endDate) summary.push('Date range');
    if (filters.registrationRequired !== undefined) summary.push('Registration filter');
    if (filters.hasAvailableSpots !== undefined) summary.push('Availability filter');
    if (filters.tags && filters.tags.length > 0) summary.push(`Tags: ${filters.tags.length}`);

    return summary.length > 0 ? summary.join(', ') : 'No filters';
  };

  const handleSaveSearch = () => {
    if (!searchName.trim()) {
      message.error('Please enter a name for the search');
      return;
    }

    if (!hasActiveFilters()) {
      message.error('No filters to save');
      return;
    }

    const newSearch: SavedSearch = {
      id: editingSearch?.id || Date.now().toString(),
      name: searchName.trim(),
      description: searchDescription.trim() || undefined,
      filters: { ...currentFilters },
      isFavorite: editingSearch?.isFavorite || false,
      createdAt: editingSearch?.createdAt || new Date(),
      lastUsed: editingSearch?.lastUsed,
    };

    if (editingSearch) {
      setSavedSearches(prev => 
        prev.map(search => search.id === editingSearch.id ? newSearch : search)
      );
      message.success('Search updated successfully');
    } else {
      setSavedSearches(prev => [...prev, newSearch]);
      message.success('Search saved successfully');
    }

    if (onSaveSearch) {
      onSaveSearch(newSearch);
    }

    setIsModalOpen(false);
    setSearchName('');
    setSearchDescription('');
    setEditingSearch(null);
  };

  const handleLoadSearch = (search: SavedSearch) => {
    onLoadSearch(search.filters);
    
    // Update last used timestamp
    setSavedSearches(prev =>
      prev.map(s => 
        s.id === search.id 
          ? { ...s, lastUsed: new Date() }
          : s
      )
    );
    
    message.success(`Loaded search: ${search.name}`);
  };

  const handleDeleteSearch = (searchId: string) => {
    setSavedSearches(prev => prev.filter(search => search.id !== searchId));
    message.success('Search deleted');
  };

  const handleToggleFavorite = (searchId: string) => {
    setSavedSearches(prev =>
      prev.map(search =>
        search.id === searchId
          ? { ...search, isFavorite: !search.isFavorite }
          : search
      )
    );
  };

  const handleEditSearch = (search: SavedSearch) => {
    setEditingSearch(search);
    setSearchName(search.name);
    setSearchDescription(search.description || '');
    setIsModalOpen(true);
  };

  const openSaveModal = () => {
    setEditingSearch(null);
    setSearchName('');
    setSearchDescription('');
    setIsModalOpen(true);
  };

  // Sort searches: favorites first, then by last used, then by created date
  const sortedSearches = [...savedSearches].sort((a, b) => {
    if (a.isFavorite && !b.isFavorite) return -1;
    if (!a.isFavorite && b.isFavorite) return 1;
    
    if (a.lastUsed && b.lastUsed) {
      return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
    }
    if (a.lastUsed && !b.lastUsed) return -1;
    if (!a.lastUsed && b.lastUsed) return 1;
    
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  return (
    <Card
      title="Saved Searches"
      size="small"
      extra={
        <Button
          type="primary"
          size="small"
          icon={<SaveOutlined />}
          onClick={openSaveModal}
          disabled={!hasActiveFilters()}
        >
          Save Current
        </Button>
      }
    >
      {sortedSearches.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="No saved searches"
          style={{ margin: '20px 0' }}
        />
      ) : (
        <List
          size="small"
          dataSource={sortedSearches}
          renderItem={(search) => (
            <List.Item
              actions={[
                <Tooltip title={search.isFavorite ? 'Remove from favorites' : 'Add to favorites'}>
                  <Button
                    type="text"
                    size="small"
                    icon={search.isFavorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                    onClick={() => handleToggleFavorite(search.id)}
                  />
                </Tooltip>,
                <Tooltip title="Edit search">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditSearch(search)}
                  />
                </Tooltip>,
                <Popconfirm
                  title="Delete this saved search?"
                  onConfirm={() => handleDeleteSearch(search.id)}
                  okText="Delete"
                  cancelText="Cancel"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                  />
                </Popconfirm>,
              ]}
            >
              <List.Item.Meta
                title={
                  <Space>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => handleLoadSearch(search)}
                      style={{ padding: 0, height: 'auto' }}
                    >
                      {search.name}
                    </Button>
                    {search.isFavorite && <StarFilled style={{ color: '#faad14', fontSize: '12px' }} />}
                  </Space>
                }
                description={
                  <Space direction="vertical" size={2}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {getFilterSummary(search.filters)}
                    </Text>
                    {search.description && (
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {search.description}
                      </Text>
                    )}
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {search.lastUsed 
                        ? `Last used: ${new Date(search.lastUsed).toLocaleDateString()}`
                        : `Created: ${new Date(search.createdAt).toLocaleDateString()}`
                      }
                    </Text>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      )}

      <Modal
        title={editingSearch ? 'Edit Saved Search' : 'Save Current Search'}
        open={isModalOpen}
        onOk={handleSaveSearch}
        onCancel={() => {
          setIsModalOpen(false);
          setSearchName('');
          setSearchDescription('');
          setEditingSearch(null);
        }}
        okText={editingSearch ? 'Update' : 'Save'}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Search Name *</Text>
            <Input
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              placeholder="Enter a name for this search"
              maxLength={50}
            />
          </div>
          
          <div>
            <Text strong>Description (Optional)</Text>
            <Input.TextArea
              value={searchDescription}
              onChange={(e) => setSearchDescription(e.target.value)}
              placeholder="Add a description to help remember what this search is for"
              rows={3}
              maxLength={200}
            />
          </div>

          <div>
            <Text strong>Current Filters:</Text>
            <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {getFilterSummary(currentFilters)}
              </Text>
            </div>
          </div>
        </Space>
      </Modal>
    </Card>
  );
};

export default SavedSearches;
