import request from 'supertest';
import { app } from '../app';
import { Event } from '../models/Event';
import { connectTestDB, closeTestDB, clearTestDB } from '../utils/testDb';
import { generateTestToken } from '../utils/testHelpers';

describe('Validation Middleware', () => {
  beforeAll(async () => {
    await connectTestDB();
  });

  afterAll(async () => {
    await closeTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('Event Validation', () => {
    const validEventData = {
      title: 'Test Event',
      description: 'A test event description',
      startDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      endDate: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(), // Tomorrow + 1 hour
      location: 'Test Location',
      venue: 'Test Venue',
      spoc: 'Test SPOC',
      contactEmail: '<EMAIL>',
      contactPhone: '+1234567890',
      department: 'TECHNOLOGY',
      type: 'MEETING',
      priority: 'MEDIUM',
      status: 'DRAFT',
    };

    const authToken = generateTestToken();

    describe('POST /api/events', () => {
      it('should create event with valid data', async () => {
        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(validEventData)
          .expect(201);

        expect(response.body.success).toBe(true);
        expect(response.body.data.title).toBe(validEventData.title);
      });

      it('should reject event with missing title', async () => {
        const invalidData = { ...validEventData };
        delete invalidData.title;

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toContain('validation');
      });

      it('should reject event with title too long', async () => {
        const invalidData = {
          ...validEventData,
          title: 'a'.repeat(201), // Exceeds 200 character limit
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'title',
              message: expect.stringContaining('200'),
            }),
          ])
        );
      });

      it('should reject event with XSS in title', async () => {
        const invalidData = {
          ...validEventData,
          title: '<script>alert("xss")</script>',
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('Invalid input detected');
      });

      it('should reject event with start date in the past', async () => {
        const invalidData = {
          ...validEventData,
          startDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Yesterday
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'startDate',
              message: expect.stringContaining('past'),
            }),
          ])
        );
      });

      it('should reject event with end date before start date', async () => {
        const invalidData = {
          ...validEventData,
          startDate: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
          endDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'endDate',
              message: expect.stringContaining('after start date'),
            }),
          ])
        );
      });

      it('should reject event with invalid email', async () => {
        const invalidData = {
          ...validEventData,
          contactEmail: 'invalid-email',
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'contactEmail',
              message: expect.stringContaining('valid email'),
            }),
          ])
        );
      });

      it('should reject event with invalid phone number', async () => {
        const invalidData = {
          ...validEventData,
          contactPhone: 'invalid-phone',
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'contactPhone',
              message: expect.stringContaining('valid phone'),
            }),
          ])
        );
      });

      it('should reject event with too many attendees', async () => {
        const invalidData = {
          ...validEventData,
          maxAttendees: 10001, // Exceeds 10,000 limit
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'maxAttendees',
              message: expect.stringContaining('10,000'),
            }),
          ])
        );
      });

      it('should reject event with registration deadline after start date', async () => {
        const invalidData = {
          ...validEventData,
          registrationRequired: true,
          registrationDeadline: new Date(Date.now() + 26 * 60 * 60 * 1000).toISOString(), // After start date
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'registrationDeadline',
              message: expect.stringContaining('before event start'),
            }),
          ])
        );
      });
    });

    describe('Security Validation', () => {
      it('should detect and reject SQL injection attempts', async () => {
        const maliciousData = {
          ...validEventData,
          title: "'; DROP TABLE events; --",
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(maliciousData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('Invalid input detected');
      });

      it('should detect and reject XSS attempts', async () => {
        const maliciousData = {
          ...validEventData,
          description: '<img src="x" onerror="alert(1)">',
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(maliciousData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('Invalid input detected');
      });

      it('should sanitize input data', async () => {
        const dataWithWhitespace = {
          ...validEventData,
          title: '  Test Event  ',
          description: '  A test description  ',
        };

        const response = await request(app)
          .post('/api/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send(dataWithWhitespace)
          .expect(201);

        expect(response.body.data.title).toBe('Test Event');
        expect(response.body.data.description).toBe('A test description');
      });
    });
  });

  describe('Registration Validation', () => {
    let eventId: string;

    beforeEach(async () => {
      const event = await Event.create({
        title: 'Test Event',
        description: 'A test event',
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 25 * 60 * 60 * 1000),
        location: 'Test Location',
        department: 'TECHNOLOGY',
        type: 'MEETING',
        priority: 'MEDIUM',
        status: 'PUBLISHED',
        registrationRequired: true,
        createdBy: 'test-user-id',
      });
      eventId = event._id.toString();
    });

    const authToken = generateTestToken();

    describe('POST /api/events/:id/register', () => {
      it('should register with valid data', async () => {
        const registrationData = {
          userEmail: '<EMAIL>',
          userName: 'Test User',
          notes: 'Test notes',
        };

        const response = await request(app)
          .post(`/api/events/${eventId}/register`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(registrationData)
          .expect(201);

        expect(response.body.success).toBe(true);
        expect(response.body.data.userEmail).toBe(registrationData.userEmail);
      });

      it('should reject registration with invalid email', async () => {
        const invalidData = {
          userEmail: 'invalid-email',
          userName: 'Test User',
        };

        const response = await request(app)
          .post(`/api/events/${eventId}/register`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'userEmail',
              message: expect.stringContaining('Valid email'),
            }),
          ])
        );
      });

      it('should reject registration with empty user name', async () => {
        const invalidData = {
          userEmail: '<EMAIL>',
          userName: '',
        };

        const response = await request(app)
          .post(`/api/events/${eventId}/register`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'userName',
              message: expect.stringContaining('between 1 and 100'),
            }),
          ])
        );
      });

      it('should reject registration with XSS in user name', async () => {
        const invalidData = {
          userEmail: '<EMAIL>',
          userName: '<script>alert("xss")</script>',
        };

        const response = await request(app)
          .post(`/api/events/${eventId}/register`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('Invalid input detected');
      });

      it('should reject registration with notes too long', async () => {
        const invalidData = {
          userEmail: '<EMAIL>',
          userName: 'Test User',
          notes: 'a'.repeat(501), // Exceeds 500 character limit
        };

        const response = await request(app)
          .post(`/api/events/${eventId}/register`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(invalidData)
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              field: 'notes',
              message: expect.stringContaining('500'),
            }),
          ])
        );
      });
    });
  });

  describe('Pagination Validation', () => {
    const authToken = generateTestToken();

    it('should accept valid pagination parameters', async () => {
      const response = await request(app)
        .get('/api/events?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should reject invalid page parameter', async () => {
      const response = await request(app)
        .get('/api/events?page=0&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('positive integer');
    });

    it('should reject invalid limit parameter', async () => {
      const response = await request(app)
        .get('/api/events?page=1&limit=101')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('between 1 and 100');
    });
  });

  describe('ObjectId Validation', () => {
    const authToken = generateTestToken();

    it('should accept valid ObjectId', async () => {
      const validId = '507f1f77bcf86cd799439011';
      
      const response = await request(app)
        .get(`/api/events/${validId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404); // Event doesn't exist, but ID format is valid

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });

    it('should reject invalid ObjectId', async () => {
      const invalidId = 'invalid-id';
      
      const response = await request(app)
        .get(`/api/events/${invalidId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid id format');
    });
  });
});
