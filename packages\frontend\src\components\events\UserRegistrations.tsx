import React, { useState } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Typography,
  Tag,
  Empty,
  Spin,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  Popconfirm,
  message,
  Alert,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  UserDeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { Event } from '@gbf-calendar/shared';

const { Title, Text } = Typography;

interface UserRegistration {
  _id: string;
  eventId: string;
  event: Event;
  userEmail: string;
  userName: string;
  registrationDate: Date;
  status: 'confirmed' | 'waitlist' | 'cancelled';
  notes?: string;
}

interface UserRegistrationsProps {
  registrations: UserRegistration[];
  loading?: boolean;
  onCancelRegistration?: (registrationId: string, eventTitle: string) => void;
  onViewEvent?: (event: Event) => void;
}

const UserRegistrations: React.FC<UserRegistrationsProps> = ({
  registrations,
  loading = false,
  onCancelRegistration,
  onViewEvent,
}) => {
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'past'>('upcoming');

  const getStatusColor = (status: string) => {
    const colors = {
      confirmed: 'green',
      waitlist: 'orange',
      cancelled: 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      confirmed: <CheckCircleOutlined />,
      waitlist: <ClockCircleOutlined />,
      cancelled: <UserDeleteOutlined />,
    };
    return icons[status as keyof typeof icons] || null;
  };

  const isEventUpcoming = (event: Event) => {
    return moment(event.startDate).isAfter(moment());
  };

  const isEventOngoing = (event: Event) => {
    const now = moment();
    return now.isBetween(moment(event.startDate), moment(event.endDate));
  };

  const getFilteredRegistrations = () => {
    const activeRegistrations = registrations.filter(reg => reg.status !== 'cancelled');
    
    switch (filter) {
      case 'upcoming':
        return activeRegistrations.filter(reg => isEventUpcoming(reg.event));
      case 'past':
        return activeRegistrations.filter(reg => !isEventUpcoming(reg.event) && !isEventOngoing(reg.event));
      default:
        return activeRegistrations;
    }
  };

  const getStats = () => {
    const active = registrations.filter(reg => reg.status !== 'cancelled');
    const upcoming = active.filter(reg => isEventUpcoming(reg.event));
    const confirmed = active.filter(reg => reg.status === 'confirmed');
    const waitlist = active.filter(reg => reg.status === 'waitlist');

    return {
      total: active.length,
      upcoming: upcoming.length,
      confirmed: confirmed.length,
      waitlist: waitlist.length,
    };
  };

  const handleCancelRegistration = (registration: UserRegistration) => {
    if (onCancelRegistration) {
      onCancelRegistration(registration._id, registration.event.title);
    }
  };

  const filteredRegistrations = getFilteredRegistrations();
  const stats = getStats();

  const renderRegistrationItem = (registration: UserRegistration) => {
    const { event } = registration;
    const isUpcoming = isEventUpcoming(event);
    const isOngoing = isEventOngoing(event);
    const canCancel = isUpcoming && registration.status !== 'cancelled';

    return (
      <List.Item
        key={registration._id}
        actions={[
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => onViewEvent?.(event)}
          >
            View
          </Button>,
          ...(canCancel ? [
            <Popconfirm
              title="Cancel Registration"
              description={`Are you sure you want to cancel your registration for "${event.title}"?`}
              onConfirm={() => handleCancelRegistration(registration)}
              okText="Cancel Registration"
              cancelText="Keep Registration"
              okType="danger"
            >
              <Button
                type="text"
                danger
                icon={<UserDeleteOutlined />}
              >
                Cancel
              </Button>
            </Popconfirm>
          ] : [])
        ]}
      >
        <List.Item.Meta
          title={
            <Space>
              <Text strong>{event.title}</Text>
              {isOngoing && <Badge status="processing" text="Live" />}
              {isUpcoming && <Badge status="default" text="Upcoming" />}
              <Tag color={getStatusColor(registration.status)} icon={getStatusIcon(registration.status)}>
                {registration.status.toUpperCase()}
              </Tag>
            </Space>
          }
          description={
            <Space direction="vertical" size={4}>
              <Space wrap>
                <Space size={4}>
                  <CalendarOutlined />
                  <Text>{moment(event.startDate).format('MMM DD, YYYY')}</Text>
                </Space>
                <Space size={4}>
                  <ClockCircleOutlined />
                  <Text>{moment(event.startDate).format('HH:mm')} - {moment(event.endDate).format('HH:mm')}</Text>
                </Space>
                {event.location && (
                  <Space size={4}>
                    <EnvironmentOutlined />
                    <Text>{event.location}</Text>
                  </Space>
                )}
              </Space>
              
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Registered: {moment(registration.registrationDate).format('MMM DD, YYYY HH:mm')}
              </Text>

              {registration.status === 'waitlist' && (
                <Alert
                  message="You're on the waitlist"
                  description="You'll be notified if a spot becomes available."
                  type="warning"
                  size="small"
                  showIcon
                />
              )}

              {registration.notes && (
                <Text type="secondary" style={{ fontSize: '12px', fontStyle: 'italic' }}>
                  Note: {registration.notes}
                </Text>
              )}
            </Space>
          }
        />
      </List.Item>
    );
  };

  if (loading) {
    return (
      <Card>
        <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '50px' }} />
      </Card>
    );
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      {/* Statistics */}
      <Card>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="Total Registrations"
              value={stats.total}
              prefix={<CalendarOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Upcoming Events"
              value={stats.upcoming}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Confirmed"
              value={stats.confirmed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Waitlist"
              value={stats.waitlist}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
        </Row>
      </Card>

      {/* Filter Buttons */}
      <Card size="small">
        <Space>
          <Text strong>Filter:</Text>
          <Button
            type={filter === 'upcoming' ? 'primary' : 'default'}
            onClick={() => setFilter('upcoming')}
            size="small"
          >
            Upcoming ({stats.upcoming})
          </Button>
          <Button
            type={filter === 'past' ? 'primary' : 'default'}
            onClick={() => setFilter('past')}
            size="small"
          >
            Past Events
          </Button>
          <Button
            type={filter === 'all' ? 'primary' : 'default'}
            onClick={() => setFilter('all')}
            size="small"
          >
            All ({stats.total})
          </Button>
        </Space>
      </Card>

      {/* Registrations List */}
      <Card title={`My Registrations (${filteredRegistrations.length})`}>
        {filteredRegistrations.length === 0 ? (
          <Empty
            description={
              filter === 'upcoming' 
                ? "No upcoming event registrations"
                : filter === 'past'
                ? "No past event registrations"
                : "No event registrations found"
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <List
            dataSource={filteredRegistrations}
            renderItem={renderRegistrationItem}
            pagination={{
              pageSize: 5,
              showSizeChanger: false,
              showQuickJumper: false,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} registrations`,
            }}
          />
        )}
      </Card>
    </Space>
  );
};

export default UserRegistrations;
