import { Request, Response } from "express";
import { authService } from "../services/authService";
import {
  LoginSchema,
  CreateUserSchema,
  ChangePasswordSchema,
  UpdateUserSchema,
} from "@gbf-calendar/shared";
import { asyncHandler } from "../middleware/errorHandler";
import { AuthenticatedRequest } from "../middleware/auth";

export class AuthController {
  // Login user
  login = asyncHandler(async (req: Request, res: Response) => {
    const loginData = LoginSchema.parse(req.body);
    const result = await authService.login(loginData);

    res.status(200).json({
      success: true,
      data: result,
      message: "Login successful",
    });
  });

  // Register new user
  register = asyncHandler(async (req: Request, res: Response) => {
    const userData = CreateUserSchema.parse(req.body);
    const result = await authService.register(userData);

    res.status(201).json({
      success: true,
      data: result,
      message: "Registration successful",
    });
  });

  // Refresh token
  refreshToken = asyncHandler(async (req: Request, res: Response) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        error: "Refresh token is required",
      });
    }

    const result = await authService.refreshToken(refreshToken);

    return res.status(200).json({
      success: true,
      data: result,
      message: "Token refreshed successfully",
    });
  });

  // Logout user (client-side token removal)
  logout = asyncHandler(async (req: Request, res: Response) => {
    // In a stateless JWT system, logout is handled client-side
    // Here we just confirm the logout
    res.status(200).json({
      success: true,
      message: "Logout successful",
    });
  });

  // Get current user profile
  getProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const user = await authService.getProfile(req.user._id);

      res.status(200).json({
        success: true,
        data: user,
        message: "Profile retrieved successfully",
      });
    }
  );

  // Update user profile
  updateProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const updateData = UpdateUserSchema.parse(req.body);
      const user = await authService.updateProfile(req.user._id, updateData);

      res.status(200).json({
        success: true,
        data: user,
        message: "Profile updated successfully",
      });
    }
  );

  // Change password
  changePassword = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { currentPassword, newPassword } = ChangePasswordSchema.parse(
        req.body
      );

      await authService.changePassword(
        req.user._id,
        currentPassword,
        newPassword
      );

      res.status(200).json({
        success: true,
        message: "Password changed successfully",
      });
    }
  );

  // Get current user info (minimal endpoint for auth checks)
  me = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    res.status(200).json({
      success: true,
      data: {
        id: req.user._id,
        email: req.user.email,
        firstName: req.user.firstName,
        lastName: req.user.lastName,
        role: req.user.role,
        status: req.user.status,
      },
      message: "User info retrieved successfully",
    });
  });
}

export const authController = new AuthController();
