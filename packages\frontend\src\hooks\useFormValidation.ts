import { useState, useCallback, useEffect } from 'react';
import { ZodSchema, ZodError } from 'zod';
import { message } from 'antd';

export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  fieldErrors: Record<string, string>;
}

export interface UseFormValidationOptions<T> {
  schema: ZodSchema<T>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  showErrorMessages?: boolean;
  debounceMs?: number;
}

export interface UseFormValidationReturn<T> {
  values: Partial<T>;
  errors: Record<string, string>;
  isValid: boolean;
  isValidating: boolean;
  hasErrors: boolean;
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, message: string) => void;
  clearError: (field: keyof T) => void;
  clearAllErrors: () => void;
  validate: (data?: Partial<T>) => Promise<ValidationResult>;
  validateField: (field: keyof T, value: any) => Promise<ValidationError | null>;
  reset: (initialValues?: Partial<T>) => void;
  getFieldError: (field: keyof T) => string | undefined;
  hasFieldError: (field: keyof T) => boolean;
}

export function useFormValidation<T extends Record<string, any>>(
  initialValues: Partial<T> = {},
  options: UseFormValidationOptions<T>
): UseFormValidationReturn<T> {
  const {
    schema,
    validateOnChange = false,
    validateOnBlur = false,
    showErrorMessages = true,
    debounceMs = 300,
  } = options;

  const [values, setValuesState] = useState<Partial<T>>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);
  const [debounceTimeouts, setDebounceTimeouts] = useState<Record<string, NodeJS.Timeout>>({});

  const clearDebounceTimeout = useCallback((field: string) => {
    if (debounceTimeouts[field]) {
      clearTimeout(debounceTimeouts[field]);
      setDebounceTimeouts(prev => {
        const newTimeouts = { ...prev };
        delete newTimeouts[field];
        return newTimeouts;
      });
    }
  }, [debounceTimeouts]);

  const validateData = useCallback(async (data: Partial<T>): Promise<ValidationResult> => {
    try {
      await schema.parseAsync(data);
      return {
        isValid: true,
        errors: [],
        fieldErrors: {},
      };
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors: ValidationError[] = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));

        const fieldErrors: Record<string, string> = {};
        validationErrors.forEach(err => {
          fieldErrors[err.field] = err.message;
        });

        return {
          isValid: false,
          errors: validationErrors,
          fieldErrors,
        };
      }
      
      return {
        isValid: false,
        errors: [{ field: 'general', message: 'Validation failed' }],
        fieldErrors: { general: 'Validation failed' },
      };
    }
  }, [schema]);

  const validate = useCallback(async (data?: Partial<T>): Promise<ValidationResult> => {
    setIsValidating(true);
    const dataToValidate = data || values;
    
    try {
      const result = await validateData(dataToValidate);
      
      if (result.isValid) {
        setErrors({});
      } else {
        setErrors(result.fieldErrors);
        if (showErrorMessages && result.errors.length > 0) {
          message.error(`Validation failed: ${result.errors[0].message}`);
        }
      }
      
      return result;
    } finally {
      setIsValidating(false);
    }
  }, [values, validateData, showErrorMessages]);

  const validateField = useCallback(async (field: keyof T, value: any): Promise<ValidationError | null> => {
    try {
      // Create a partial object with just this field for validation
      const fieldData = { [field]: value } as Partial<T>;
      const result = await validateData({ ...values, ...fieldData });
      
      const fieldError = result.fieldErrors[field as string];
      if (fieldError) {
        return {
          field: field as string,
          message: fieldError,
        };
      }
      
      return null;
    } catch (error) {
      return {
        field: field as string,
        message: 'Validation error',
      };
    }
  }, [values, validateData]);

  const setValue = useCallback((field: keyof T, value: any) => {
    setValuesState(prev => ({ ...prev, [field]: value }));
    
    // Clear existing error for this field
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field as string];
      return newErrors;
    });

    // Validate on change if enabled
    if (validateOnChange) {
      clearDebounceTimeout(field as string);
      
      const timeout = setTimeout(async () => {
        const error = await validateField(field, value);
        if (error) {
          setErrors(prev => ({ ...prev, [field as string]: error.message }));
        }
      }, debounceMs);

      setDebounceTimeouts(prev => ({ ...prev, [field as string]: timeout }));
    }
  }, [validateOnChange, validateField, debounceMs, clearDebounceTimeout]);

  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }));
    
    if (validateOnChange) {
      // Clear all debounce timeouts
      Object.values(debounceTimeouts).forEach(clearTimeout);
      setDebounceTimeouts({});
      
      // Validate after a delay
      const timeout = setTimeout(async () => {
        await validate({ ...values, ...newValues });
      }, debounceMs);

      setDebounceTimeouts({ general: timeout });
    }
  }, [validateOnChange, validate, values, debounceMs, debounceTimeouts]);

  const setError = useCallback((field: keyof T, message: string) => {
    setErrors(prev => ({ ...prev, [field as string]: message }));
  }, []);

  const clearError = useCallback((field: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field as string];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  const reset = useCallback((newInitialValues?: Partial<T>) => {
    const resetValues = newInitialValues || initialValues;
    setValuesState(resetValues);
    setErrors({});
    setIsValidating(false);
    
    // Clear all debounce timeouts
    Object.values(debounceTimeouts).forEach(clearTimeout);
    setDebounceTimeouts({});
  }, [initialValues, debounceTimeouts]);

  const getFieldError = useCallback((field: keyof T): string | undefined => {
    return errors[field as string];
  }, [errors]);

  const hasFieldError = useCallback((field: keyof T): boolean => {
    return !!(errors[field as string]);
  }, [errors]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(debounceTimeouts).forEach(clearTimeout);
    };
  }, [debounceTimeouts]);

  const isValid = Object.keys(errors).length === 0;
  const hasErrors = Object.keys(errors).length > 0;

  return {
    values,
    errors,
    isValid,
    isValidating,
    hasErrors,
    setValue,
    setValues,
    setError,
    clearError,
    clearAllErrors,
    validate,
    validateField,
    reset,
    getFieldError,
    hasFieldError,
  };
}
