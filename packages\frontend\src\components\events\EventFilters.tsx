import React, { useState, useEffect } from 'react';
import {
  Card,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Row,
  Col,
  Collapse,
  Tag,
  Switch,
  Divider,
  Typography,
  Badge,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  CalendarOutlined,
  TagsOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import {
  EventType,
  EventPriority,
  EventStatus,
  Department,
  RegistrationStatus,
  EventFilters as IEventFilters,
} from '@gbf-calendar/shared';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;
const { Text } = Typography;

interface EventFiltersProps {
  onFiltersChange: (filters: IEventFilters) => void;
  loading?: boolean;
  initialFilters?: Partial<IEventFilters>;
}

const EventFilters: React.FC<EventFiltersProps> = ({
  onFiltersChange,
  loading = false,
  initialFilters = {},
}) => {
  const [searchText, setSearchText] = useState(initialFilters.search || '');
  const [selectedTypes, setSelectedTypes] = useState<EventType[]>(initialFilters.type || []);
  const [selectedStatuses, setSelectedStatuses] = useState<EventStatus[]>(initialFilters.status || []);
  const [selectedPriorities, setSelectedPriorities] = useState<EventPriority[]>(initialFilters.priority || []);
  const [selectedDepartments, setSelectedDepartments] = useState<Department[]>(initialFilters.department || []);
  const [selectedRegistrationStatuses, setSelectedRegistrationStatuses] = useState<RegistrationStatus[]>(
    initialFilters.registrationStatus || []
  );
  const [dateRange, setDateRange] = useState<[moment.Moment | null, moment.Moment | null]>([
    initialFilters.startDate ? moment(initialFilters.startDate) : null,
    initialFilters.endDate ? moment(initialFilters.endDate) : null,
  ]);
  const [location, setLocation] = useState(initialFilters.location || '');
  const [registrationRequired, setRegistrationRequired] = useState<boolean | undefined>(
    initialFilters.registrationRequired
  );
  const [hasAvailableSpots, setHasAvailableSpots] = useState<boolean | undefined>(
    initialFilters.hasAvailableSpots
  );
  const [selectedTags, setSelectedTags] = useState<string[]>(initialFilters.tags || []);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      handleFiltersChange();
    }, 300);

    return () => clearTimeout(timer);
  }, [searchText, location]);

  const handleFiltersChange = () => {
    const filters: IEventFilters = {
      search: searchText || undefined,
      type: selectedTypes.length > 0 ? selectedTypes : undefined,
      status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
      priority: selectedPriorities.length > 0 ? selectedPriorities : undefined,
      department: selectedDepartments.length > 0 ? selectedDepartments : undefined,
      registrationStatus: selectedRegistrationStatuses.length > 0 ? selectedRegistrationStatuses : undefined,
      startDate: dateRange[0]?.toDate(),
      endDate: dateRange[1]?.toDate(),
      location: location || undefined,
      registrationRequired,
      hasAvailableSpots,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
    };

    onFiltersChange(filters);
  };

  const clearAllFilters = () => {
    setSearchText('');
    setSelectedTypes([]);
    setSelectedStatuses([]);
    setSelectedPriorities([]);
    setSelectedDepartments([]);
    setSelectedRegistrationStatuses([]);
    setDateRange([null, null]);
    setLocation('');
    setRegistrationRequired(undefined);
    setHasAvailableSpots(undefined);
    setSelectedTags([]);
    
    onFiltersChange({});
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (searchText) count++;
    if (selectedTypes.length > 0) count++;
    if (selectedStatuses.length > 0) count++;
    if (selectedPriorities.length > 0) count++;
    if (selectedDepartments.length > 0) count++;
    if (selectedRegistrationStatuses.length > 0) count++;
    if (dateRange[0] || dateRange[1]) count++;
    if (location) count++;
    if (registrationRequired !== undefined) count++;
    if (hasAvailableSpots !== undefined) count++;
    if (selectedTags.length > 0) count++;
    return count;
  };

  const formatEnumValue = (value: string) => {
    return value.replace(/_/g, ' ').toUpperCase();
  };

  return (
    <Card
      title={
        <Space>
          <FilterOutlined />
          <Text strong>Filters</Text>
          <Badge count={getActiveFiltersCount()} showZero={false} />
        </Space>
      }
      extra={
        <Button
          type="text"
          icon={<ClearOutlined />}
          onClick={clearAllFilters}
          disabled={getActiveFiltersCount() === 0}
        >
          Clear All
        </Button>
      }
      size="small"
    >
      {/* Search */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Input
            placeholder="Search events by title, description, location, or SPOC..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </Col>
      </Row>

      {/* Quick Filters */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Input
            placeholder="Filter by location"
            prefix={<CalendarOutlined />}
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            allowClear
          />
        </Col>
        <Col span={12}>
          <RangePicker
            style={{ width: '100%' }}
            placeholder={['Start Date', 'End Date']}
            value={dateRange}
            onChange={(dates) => {
              setDateRange(dates || [null, null]);
              setTimeout(handleFiltersChange, 0);
            }}
            allowClear
          />
        </Col>
      </Row>

      {/* Advanced Filters */}
      <Collapse ghost style={{ marginTop: 16 }}>
        <Panel header="Advanced Filters" key="advanced">
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* Event Type */}
            <div>
              <Text strong>Event Type:</Text>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select event types"
                value={selectedTypes}
                onChange={(values) => {
                  setSelectedTypes(values);
                  setTimeout(handleFiltersChange, 0);
                }}
                allowClear
              >
                {Object.values(EventType).map(type => (
                  <Option key={type} value={type}>
                    {formatEnumValue(type)}
                  </Option>
                ))}
              </Select>
            </div>

            {/* Department */}
            <div>
              <Text strong>Department:</Text>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select departments"
                value={selectedDepartments}
                onChange={(values) => {
                  setSelectedDepartments(values);
                  setTimeout(handleFiltersChange, 0);
                }}
                allowClear
              >
                {Object.values(Department).map(dept => (
                  <Option key={dept} value={dept}>
                    {formatEnumValue(dept)}
                  </Option>
                ))}
              </Select>
            </div>

            {/* Status */}
            <div>
              <Text strong>Status:</Text>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select statuses"
                value={selectedStatuses}
                onChange={(values) => {
                  setSelectedStatuses(values);
                  setTimeout(handleFiltersChange, 0);
                }}
                allowClear
              >
                {Object.values(EventStatus).map(status => (
                  <Option key={status} value={status}>
                    {formatEnumValue(status)}
                  </Option>
                ))}
              </Select>
            </div>

            {/* Priority */}
            <div>
              <Text strong>Priority:</Text>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select priorities"
                value={selectedPriorities}
                onChange={(values) => {
                  setSelectedPriorities(values);
                  setTimeout(handleFiltersChange, 0);
                }}
                allowClear
              >
                {Object.values(EventPriority).map(priority => (
                  <Option key={priority} value={priority}>
                    {formatEnumValue(priority)}
                  </Option>
                ))}
              </Select>
            </div>

            <Divider />

            {/* Registration Filters */}
            <div>
              <Text strong>Registration:</Text>
              <div style={{ marginTop: 8 }}>
                <Space direction="vertical">
                  <div>
                    <Text>Registration Required: </Text>
                    <Switch
                      checked={registrationRequired}
                      onChange={(checked) => {
                        setRegistrationRequired(checked ? true : undefined);
                        setTimeout(handleFiltersChange, 0);
                      }}
                      checkedChildren="Yes"
                      unCheckedChildren="Any"
                    />
                  </div>
                  <div>
                    <Text>Has Available Spots: </Text>
                    <Switch
                      checked={hasAvailableSpots}
                      onChange={(checked) => {
                        setHasAvailableSpots(checked ? true : undefined);
                        setTimeout(handleFiltersChange, 0);
                      }}
                      checkedChildren="Yes"
                      unCheckedChildren="Any"
                    />
                  </div>
                </Space>
              </div>
            </div>

            {/* Registration Status */}
            <div>
              <Text strong>Registration Status:</Text>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select registration statuses"
                value={selectedRegistrationStatuses}
                onChange={(values) => {
                  setSelectedRegistrationStatuses(values);
                  setTimeout(handleFiltersChange, 0);
                }}
                allowClear
              >
                {Object.values(RegistrationStatus).map(status => (
                  <Option key={status} value={status}>
                    {formatEnumValue(status)}
                  </Option>
                ))}
              </Select>
            </div>
          </Space>
        </Panel>
      </Collapse>

      {/* Active Filters Display */}
      {getActiveFiltersCount() > 0 && (
        <div style={{ marginTop: 16 }}>
          <Text strong>Active Filters:</Text>
          <div style={{ marginTop: 8 }}>
            <Space wrap>
              {searchText && <Tag closable onClose={() => setSearchText('')}>Search: {searchText}</Tag>}
              {location && <Tag closable onClose={() => setLocation('')}>Location: {location}</Tag>}
              {selectedTypes.map(type => (
                <Tag
                  key={type}
                  closable
                  onClose={() => {
                    setSelectedTypes(prev => prev.filter(t => t !== type));
                    setTimeout(handleFiltersChange, 0);
                  }}
                >
                  Type: {formatEnumValue(type)}
                </Tag>
              ))}
              {selectedDepartments.map(dept => (
                <Tag
                  key={dept}
                  closable
                  onClose={() => {
                    setSelectedDepartments(prev => prev.filter(d => d !== dept));
                    setTimeout(handleFiltersChange, 0);
                  }}
                >
                  Dept: {formatEnumValue(dept)}
                </Tag>
              ))}
              {selectedStatuses.map(status => (
                <Tag
                  key={status}
                  closable
                  onClose={() => {
                    setSelectedStatuses(prev => prev.filter(s => s !== status));
                    setTimeout(handleFiltersChange, 0);
                  }}
                >
                  Status: {formatEnumValue(status)}
                </Tag>
              ))}
            </Space>
          </div>
        </div>
      )}
    </Card>
  );
};

export default EventFilters;
