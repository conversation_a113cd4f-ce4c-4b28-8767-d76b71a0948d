import React, { useState, useEffect } from "react";
import {
  Layout,
  <PERSON>u,
  Button,
  Avatar,
  Dropdown,
  Typography,
  Drawer,
} from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MenuOutlined,
  DashboardOutlined,
  CalendarOutlined,
  UnorderedListOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { useNavigate, useLocation } from "react-router-dom";
import styled from "styled-components";

import { useAuthStore } from "@/store";
import { useLogout } from "@/hooks";
import { BaseComponentProps } from "@/types";
import { media, touchTargets } from "@/styles/breakpoints";
import MobileBottomNav from "./MobileBottomNav";
import OfflineIndicator from "../common/OfflineIndicator";
import GlobalSearch from "../common/GlobalSearch";

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

const StyledHeader = styled(Header)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 56px;

  ${media.md} {
    padding: 0 24px;
    height: 64px;
  }
`;

const MobileMenuButton = styled(Button)`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: ${touchTargets.comfortable};

  ${media.md} {
    display: none;
  }
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;

  ${media.md} {
    gap: 12px;
    font-size: 18px;
  }

  ${media.maxSm} {
    font-size: 14px;

    .logo-text {
      display: none;
    }
  }
`;

// const Logo = styled.div`
//   display: flex;
//   align-items: center;
//   gap: 12px;
//   font-size: 18px;
//   font-weight: 600;
//   color: #1890ff;
// `;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;

  ${media.md} {
    gap: 12px;
  }

  .user-name {
    ${media.maxSm} {
      display: none;
    }
  }
`;

interface AppLayoutProps extends BaseComponentProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuthStore();
  const logoutMutation = useLogout();

  // Check if screen is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Close mobile drawer when route changes
  useEffect(() => {
    setMobileDrawerOpen(false);
  }, [location.pathname]);

  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        navigate("/login");
      },
    });
  };

  const menuItems = [
    {
      key: "/dashboard",
      icon: <DashboardOutlined />,
      label: "Dashboard",
    },
    {
      key: "/calendar",
      icon: <CalendarOutlined />,
      label: "Calendar",
    },
    {
      key: "/events",
      icon: <UnorderedListOutlined />,
      label: "Events",
    },
    {
      key: "/departments",
      icon: <UserOutlined />,
      label: "Departments",
    },
  ];

  const userMenuItems = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "Profile",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "Settings",
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "Logout",
      onClick: handleLogout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const sidebarContent = (
    <>
      <div style={{ padding: "16px", textAlign: "center" }}>
        <Title level={4} style={{ margin: 0, color: "#1890ff" }}>
          {collapsed && !isMobile ? "GBF" : "GBF Calendar"}
        </Title>
      </div>

      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ border: "none" }}
      />
    </>
  );

  return (
    <Layout style={{ minHeight: "100vh" }}>
      {/* Desktop Sidebar */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          style={{
            background: "#fff",
            boxShadow: "2px 0 8px rgba(0, 0, 0, 0.06)",
          }}
        >
          {sidebarContent}
        </Sider>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Drawer
          title="GBF Calendar"
          placement="left"
          onClose={() => setMobileDrawerOpen(false)}
          open={mobileDrawerOpen}
          bodyStyle={{ padding: 0 }}
          headerStyle={{ borderBottom: "1px solid #f0f0f0" }}
        >
          {sidebarContent}
        </Drawer>
      )}

      <Layout>
        <StyledHeader>
          <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
            {isMobile ? (
              <MobileMenuButton
                type="text"
                icon={<MenuOutlined />}
                onClick={() => setMobileDrawerOpen(true)}
              />
            ) : (
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                style={{ fontSize: "16px", width: 40, height: 40 }}
              />
            )}

            <Logo>
              <CalendarOutlined />
              <span className="logo-text">GBF Calendar</span>
            </Logo>
          </div>

          {/* Global Search - Hidden on mobile */}
          {!isMobile && (
            <div style={{ flex: 1, maxWidth: 400, margin: "0 24px" }}>
              <GlobalSearch
                placeholder="Search events, locations, organizers..."
                style={{ width: "100%" }}
              />
            </div>
          )}

          <UserSection>
            <span className="user-name">Welcome, {user?.firstName}!</span>
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={["click"]}
            >
              <Avatar
                style={{ cursor: "pointer", backgroundColor: "#1890ff" }}
                icon={<UserOutlined />}
              >
                {user?.firstName?.[0]?.toUpperCase()}
              </Avatar>
            </Dropdown>
          </UserSection>
        </StyledHeader>

        <Content
          style={{
            padding: "24px",
            background: "#f5f5f5",
            paddingBottom: isMobile ? "80px" : "24px", // Add bottom padding for mobile nav
          }}
        >
          {children}
        </Content>
      </Layout>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav />

      {/* Offline Indicator */}
      <OfflineIndicator />
    </Layout>
  );
};

export default AppLayout;
